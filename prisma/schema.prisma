generator client {
  provider     = "prisma-client-js"
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model Projects {
  id          Int      @id @default(autoincrement())
  title       String
  slug        String   @unique
  description String
  image       String
  link_demo   String?
  link_github String?
  stacks      String
  is_show     <PERSON>olean  @default(true)
  updated_at  DateTime @default(now())
  content     String?  @db.Text
  is_featured Boolean  @default(false)
}

model ContentMeta {
  id          Int      @id @default(autoincrement())
  slug        String   @unique
  type        String
  views       Int      @default(0)
  updated_at  DateTime @default(now())
}
