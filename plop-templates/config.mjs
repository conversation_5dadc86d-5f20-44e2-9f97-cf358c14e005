/**
 * 项目配置文件
 * 包含各种生成器使用的配置项
 */

// 项目状态
export const projectStates = ['开发中' , '维护中' , '待发布' , '待更新' , '停止更新']

// 技术栈列表, 完整版在`/src/contents/stacks.tsx`
export const STACK_CHOICES = [
  'TypeScript',
  'Next.js',
  'React.js',
  'TailwindCSS',
  'Bootstrap',
  'Vite',
  'Prisma',
  'Firebase',
  'Artificial Intelligence',
  'Angular',
  'Vue.js',
  'Nuxt.js',
  'Node.js',
  'Nest.js',
  'Ant Design',
  'Swiper',
  'Gatsby',
  'PWA',
  'Nginx',
  'Jest',
  'Storybook',
  'Socket',
  'Remix',
  'Express',
  'Vben',
  'Electron'
];

// cheatsheet 使用的 type 字段
export const cheatsheetTypes = [
'gitlab',
'lerna',
'prisma',
'ts',
'bash',
'zsh',
'sh',
'linux',
'Pinia',
'css3',
'go',
'less',
'ps',
'vant',
'actionvue',
'golang',
'mysql',
'psv',
'vim',
'angular',
'docker',
'html',
'nestjs',
'python',
'vite',
'antdv',
'eleplus',
'nextjs',
'raspberrypi',
'vscode',
'axios',
'ele',
'java',
'nginx',
'reactnative',
'vue',
'babel',
'es',
'nodejs',
'react',
'webpack',
'caddy',
'firefox',
'jenkins',
'npm',
'sass',
'wordpress',
'chrome',
'flutter',
'js',
'php',
'shopify',
'yarn',
'git',
'k8s',
'postcss',
'tailwindcss',
'terminal',
]
