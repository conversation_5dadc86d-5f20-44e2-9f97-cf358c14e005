---
title: 第 01 期 - 2024 元旦
slug: issue-01
draft: false
date: '2024-01-01'
description: '这是「果酱前端周刊」第一期，以后每周都会把看到的有趣的工具、开源项目、文章发布到周刊，欢迎关注。'
cover_url: 'https://static.webjam.cn/images/202401/firework.webp'
created_at: '2024-01-01'
updated_at: '2024-01-07'
---

![封面图](https://static.webjam.cn/images/202401/firework.webp)

封面图是元旦晚上观看的烟花。

> 这是「果酱前端周刊」第一期，以后每周都会把看到的有趣的工具、开源项目、文章发布到周刊，欢迎关注。

## 开源和工具

#### 1、 OrbStack

[OrbStack · Fast, light, simple Docker & Linux on macOS](https://orbstack.dev/)

轻量级的 Docker 和 Linux 虚拟机管理工具，仅支持 macOS。而且会为每一个 Docker 容器配置一个 https 域名。

![OrbStack](https://static.webjam.cn/images/202401/orbstack.webp)

#### 2、Vectorizer.AI

[Trace Pixels To Vectors in Full Color, Fully Automatically, Using AI - Vectorizer.AI](https://vectorizer.ai/)

这个工具可以把任意 jpg/png 转化为真正的 svg 矢量图，最高 2w 像素。

_目前直接在网页使用是免费的，如果调用 API 接口收费。_

![Vectorizer.AI](https://static.webjam.cn/images/202401/vectorizer.webp)

#### 3、Mantine UI

[Mantine](https://mantine.dev/)

一个基于 React 的 UI 组件库，样式精美，代码优雅。如果你厌倦了国内的几个 UI 库，不妨试试这个。

#### 4、 primevue

[PrimeVue - Vue UI Component Library](https://primevue.org/)

一个基于 Vue.js 的 UI 组件库，组件功能非常丰富，样式好看，API 设计优雅，有各式各样的主题。

而且开发团队还提供了 React 和 Angular 版本的组件库，有兴趣可以去看看。

## 随便看看

1、[何为 Bento 式布局，怎么生产力工具网站都在用？ - 少数派 (sspai.com)](https://sspai.com/post/84628)

![Bento 式布局](https://static.webjam.cn/images/202401/bento.webp)

> 其原理就是基于网格来组织 UI 布局以有效传达信息，说白了就是多了种 card 布局思路。

「Bento 式布局」也可以叫做便当 🍱 布局，更通俗理解就是盒子布局，这篇文章既有科普又有实战（最后有如何使用 figma 实现）

2、[妨碍成长的 6 个信念，来看看你有没有 | 小强的时间管理博客 (gtdlife.com)](https://www.gtdlife.com/2023/6410/chengzhangxinnian/)

文章提到的“妨碍成长的 6 个信念”来自《重塑心灵》这本书，这六个信念主要是帮你去掉一些心理上的顾虑，做更积极的自己。

![成长信念](https://static.webjam.cn/images/202401/chengzhangxinnian.webp)
