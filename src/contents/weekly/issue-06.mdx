---
title: 第 06 期 - 锡惠公园
slug: issue-06
draft: false
date: '2024-05-11'
description: '这是「果酱前端周刊」第五期，以后每周都会把看到的有趣的工具、开源项目、文章发布到周刊，欢迎关注。'
cover_url: 'https://static.webjam.cn/images/202401/xi-hui-gong-yuan.webp'
created_at: '2024-05-11'
updated_at: '2024-05-11'
---

![封面图](https://static.webjam.cn/images/202401/xi-hui-gong-yuan.webp)

封面图来自无锡锡惠公园

> 这是「果酱前端周刊」第六期，以后每周都会把看到的有趣的工具、开源项目、文章发布到周刊，欢迎关注。

## 开源

## 工具

### Gmail 无限别名邮箱生成器 (https://gmail.inputekno.com/)

输入一个 Gmail 地址，它可以生成多个 Gmail 地址

![](https://static.webjam.cn/images/202405/gmail-trick.webp)

原理是 Gmail 会自动删除电子邮件地址中的点「.」，以防止电子邮件中出现拼写错误。而大多数网站会认为带有「.」的电子邮件是一个新的电子邮件

### [regex-vis.com 可视化正则表达式](https://regex-vis.com/)

![](https://static.webjam.cn/images/202405/regex-vis.webp)

输入一个正则表达式后，会生成它的可视化图形。然后可以点选或框选图形中的单个或多个节点，再在右侧操作面板对其进行操作，具体操作取决于节点的类型，比如在其右侧插入空节点、为节点编组、为节点增加量词等

详细介绍看: [使用 regex-vis.com 可视化正则表达式](https://bowencodes.com/post/regex-vis)

### [regex101 正则表达式在线验证工具](https://regex101.com/)

这是我遇到的功能最强的正则工具了,

### [循序渐进地学习正则表达式，从零到高级](https://regexlearn.com/zh-cn/learn/regex101)

交互式的正则学习网站, 支持多种语言, 如果你想学习正则表达式, 请一定要试一下这个网站. 同时网站代码还是开源的: [aykutkardas/regexlearn.com: Learn RegEx step by step, from zero to advanced.](https://github.com/aykutkardas/regexlearn.com)

![](https://static.webjam.cn/images/202405/regexlearn.webp)

## 独立开发

### [出海去孵化器](https://chuhaiqu.club/)

![](https://static.webjam.cn/images/202405/chuhaiqu.webp)

标语是发掘「超级个体」潜力, 打造自己的全球化「一人公司」, 有兴趣做独立开发者的小伙伴不妨关注一下
