---
title: 第 03 期 - 贡湖湾湿地公园
slug: issue-03
draft: false
date: '2024-01-15'
description: '这是「果酱前端周刊」第三期，以后每周都会把看到的有趣的工具、开源项目、文章发布到周刊，欢迎关注。'
cover_url: 'https://static.webjam.cn/images/202401/gonghuwan.webp'
created_at: '2024-01-11'
updated_at: '2024-01-17'
---

![封面图](https://static.webjam.cn/images/202401/gonghuwan.webp)

封面图来自无锡贡湖湾湿地公园，近处是彩虹 🌈 跑道，远处湖天一色。

> 这是「果酱前端周刊」第三期，以后每周都会把看到的有趣的工具、开源项目、文章发布到周刊，欢迎关注。

## 开源和工具

1、[WakaTime](https://wakatime.com/)

一个记录编码时间的工具。

WakaTime 为几十种编辑器提供了插件，方便用户在各个端记录使用时间。

WakaTime 为免费用户保留 14 天的历史记录，你可以自建数据库永久保存 WakaTime 数据。

2、[degit](https://github.com/Rich-Harris/degit)

我认为 degit 相对于 `git clone` 的最主要优点是**不携带 git 历史信息**，这在将一个 Git 仓库当作项目模板时很有用，也因此使用 degit 下载更快，因为不需要下载整个 Git 历史记录。

## 随便看看

1、[豆瓣 2023 年度电影榜单](https://movie.douban.com/annual/2023/)

榜单上列出来我只看了很少一部分，后面想看剧的时候就可以从榜单上面挑了。

2、[我在阿里做开发的高效打工技巧总结](https://mp.weixin.qq.com/s/J0kHPMN9gXyDmCMce9qmCQ)

文章总结了很多我们打工人工作时的痛点和解决方案，如果大家都能尽量按照这种方式，那打工幸福指数会提高很多。

3、[Find N3「全景虚拟屏」浅析：谈到大屏适配，我们究竟在谈些什么？ - 少数派](https://sspai.com/post/84641)

「全景虚拟屏」是个很有意思的创意，不过我个人几乎所有的工作即使是看视频都在电脑上完成，除非手边只有手机，所以目前来讲吸引力不是很大。

https://app.heptabase.com/w/c70deeb6888eaa0f243f75db4914cc7997221ef9dec4c8d6165ff7c22469c203?id=29a5e0c7-7661-47b8-a1bc-4b511e0a8ce5
