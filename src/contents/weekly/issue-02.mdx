---
title: 第 02 期 - 南航
slug: issue-02
draft: false
date: '2024-01-11'
description: '这是「果酱前端周刊」第二期，以后每周都会把看到的有趣的工具、开源项目、文章发布到周刊，欢迎关注。'
cover_url: 'https://static.webjam.cn/images/202401/plane.jpg'
created_at: '2024-01-11'
updated_at: '2024-01-12'
---

![封面图](https://static.webjam.cn/images/202401/plane.jpg)

封面图来自南京航空航天大学江宁校区御风园内

> 这是「果酱前端周刊」第二期，以后每周都会把看到的有趣的工具、开源项目、文章发布到周刊，欢迎关注。

## 开源和工具

1、[Astro](https://astro.build/)

如果你想要搭建一个以内容为主的网站，可以尝试一下 [Astro](https://astro.build/)。

它有以下优点：

- 群岛：一种基于组件的针对内容驱动的网站进行优化的 Web 架构。
- UI 无关：支持 React、Preact、Svelte、Vue、Solid、Lit、HTMX、Web 组件等等。
- 服务器优先：将沉重的渲染移出访问者的设备。
- 默认无 JS：更少减慢你网站速度的客户端 JavaScript。
- 内容集合：组织、验证你的 Markdown 内容，并提供 TypeScript 类型安全。
- 可定制：Tailwind、MDX 和数百个集成可供选择。

2、[earthworm](https://earthworm.cuixueshe.com/)

一个零基础学习英语的网站，具体学习方法是先看一遍视频课: [零基础学英语](https://www.bilibili.com/video/BV13g411F7Kd/)，然后再基于 earthworm 刷句子，不断地练习。

同时这个网站代码是开源的： [https://github.com/cuixiaorui/earthworm](https://github.com/cuixiaorui/earthworm)，你可以本地部署或者贡献自己的代码。

3、 [millsp/ts-toolbelt](https://github.com/millsp/ts-toolbelt)

👷 强大的 TypeScript 类型实用程序库 --- 借助它你可以少写很多类型工具函数

## 随便看看

1、[Matrix Talk | 极简桌面、优质平替……这是我们的桌搭进化之路 - 少数派](https://sspai.com/post/85556)

喜欢折腾桌面的可以参考，将来这也是我在家里搭建桌面的一个参考依据。

2、[🌈 通往 AGI 之路](https://waytoagi.feishu.cn/wiki/QPe5w5g7UisbEkkow8XcDmOpn8e?chunked=false)

一个 AI 大合集，不过不是简单的把 AI 产品放在一起，是更深入更有条理地整理了从 AI 的历史、思想和概念到 Prompt 到一些课程和产品。
