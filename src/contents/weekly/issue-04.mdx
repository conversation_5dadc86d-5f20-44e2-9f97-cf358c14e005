---
title: 第 04 期 - 金城湾公园
slug: issue-04
draft: false
date: '2024-01-19'
description: '这是「果酱前端周刊」第四期，以后每周都会把看到的有趣的工具、开源项目、文章发布到周刊，欢迎关注。'
cover_url: 'https://static.webjam.cn/images/202401/jin-cheng-wan-gong-yuan.webp'
created_at: '2024-01-11'
updated_at: '2024-01-17'
---

![封面图](https://static.webjam.cn/images/202401/jin-cheng-wan-gong-yuan.webp)

封面图来自无锡金城湾公园。

> 这是「果酱前端周刊」第四期，以后每周都会把看到的有趣的工具、开源项目、文章发布到周刊，欢迎关注。

## 开源和工具

1、[Linear – A better way to build products](https://linear.app/)

Linear 是一款研发项目管理工具，我目前正在使用它管理我个人的一些项目。这里有两篇文章也许对你理解 Linear 有些帮助：

- [你好，生产力(番外篇 1) - Linear, by the developer, for the developer-阿里云开发者社区](https://developer.aliyun.com/article/769395)
- [什么是 Linear 设计风格？ - 少数派](https://sspai.com/post/79347#!)

![Linear](https://static.webjam.cn/images/202401/linearapp.webp)

2、[Vocs – React Documentation Framework](https://vocs.dev/)

基于 React + Vite 开发的极简文档框架，界面风格非常极简，和 [vitepress](https://vitepress.dev/) 很相似，不过一个是基于 React 的，一个是基于 Vue 的。

![vocs](https://static.webjam.cn/images/202401/vocs.webp)

3、[nygardk/react-share: Social media share buttons and share counts for React](https://github.com/nygardk/react-share?tab=readme-ov-file)

React 实现的设计媒体分享按钮，还支持显示分享次数，支持 tree shaking。

![react-share](https://static.webjam.cn/images/202401/react-share.webp)

4、[Prismane](https://www.prismane.io/)

一个非常年轻的 React UI 库，已经拥有非常多的组件和 hooks 以及表单校验规则。目前 Star 不算特别多，可以持续关注。

![Prismane](https://static.webjam.cn/images/202401/prismane.webp)

## 文章

1、[开发同学的“做事情”&“想事情”&“谈事情”](https://developer.aliyun.com/article/1403806?utm_content=g_1000387260)

作者抛开技术栈和方案经验等这些具体的内容，从做事情、想事情、谈事情三个方面总结了自己的一些感悟。

2、[《如何获得创业想法》 总结](https://app.heptabase.com/w/c70deeb6888eaa0f243f75db4914cc7997221ef9dec4c8d6165ff7c22469c203?id=29a5e0c7-7661-47b8-a1bc-4b511e0a8ce5)

![如何获得创业想法](https://static.webjam.cn/images/202401/202401252308281.webp)

3、[The Shapes of CSS | CSS-Tricks - CSS-Tricks](https://css-tricks.com/the-shapes-of-css/)

使用纯 CSS 实现各种形状，越往后看形状越复杂有趣，比如心形、太极图、Facebook Logo、锁。

![The Shapes of CSS](https://static.webjam.cn/images/202401/css-heart.webp)

4、[Demo: Pure CSS speech bubbles – Nicolas Gallagher](https://nicolasgallagher.com/pure-css-speech-bubbles/demo/)

使用纯 CSS 实现各种聊天气泡

![Pure CSS speech bubbles](https://static.webjam.cn/images/202401/pure-css-speech-bubbles.webp)

## 有趣

1、[[植物大战僵尸]自制关卡 胆小菇之梦](https://www.bilibili.com/video/BV1ZT4y1875k/?share_source=copy_web&vd_source=cde8df2b3fa2127859ddf7c1e4250ff4)

「胆小菇之梦」自制关卡，看的过程中**不断地被惊喜和震撼**，它有着自己的一套很奇特的关卡机制。我估计很多人都很熟悉这个游戏的原版，不过如果让你玩这个自制关卡，你还有多少信心呢？

> 关卡机制：
>
> - 胆小菇不论前方有无僵尸，不断射击。
> - 其他植物仅在被子弹命中时发射子弹。
> - 双发被子弹命中后会发射两发子弹，但有上限：每游戏刻最多一颗。
> - 卷心菜向上射击。 -磁力菇会使周围子弹转弯。

![植物大战僵尸 - 胆小菇之梦](https://static.webjam.cn/images/202401/pvz.webp)
