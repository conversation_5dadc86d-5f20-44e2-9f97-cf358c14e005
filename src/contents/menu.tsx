import { BiRocket as ContactIcon } from 'react-icons/bi';
import {
  BsEnvelopeAtFill as EmailIcon,
  BsGithub as GithubIcon,
  BsInstagram as InstagramIcon,
  BsLinkedin as LinkedinIcon,
  BsTwitter as TwitterIcon,
} from 'react-icons/bs';
import {
  FiBook<PERSON>pen as LearnIcon,
  FiCode as SnippetsIcon,
  FiCoffee as ProjectIcon,
  FiCpu as DashboardIcon,
  FiLayers as WeeklyIcon,
  FiPieChart as AnalyticsIcon,
  FiPocket as HomeIcon,
  FiRss as BlogIcon,
  FiUser as ProfileIcon,
} from 'react-icons/fi';
// import { PiChatCircleDotsBold as ChatIcon } from 'react-icons/pi';
import { SiJavascript } from 'react-icons/si';

import { MenuItemProps } from '@/common/types/menu';
import { siteMetadata, socialAccounts } from '@/contents/siteMetadata';

const iconSize = 20;

export const MENU_ITEMS: MenuItemProps[] = [
  {
    title: 'Home',
    href: '/',
    icon: <HomeIcon size={iconSize} />,
    isShow: true,
    isExternal: false,
    eventName: 'Pages: Home',
    type: 'Pages',
  },
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: <DashboardIcon size={iconSize} />,
    isShow: true,
    isExternal: false,
    eventName: 'Pages: Dashboard',
    type: 'Pages',
  },
  {
    title: 'Projects',
    href: '/projects',
    icon: <ProjectIcon size={iconSize} />,
    isShow: true,
    isExternal: false,
    eventName: 'Pages: Projects',
    type: 'Pages',
  },
  {
    title: 'Blog',
    href: '/blog',
    icon: <BlogIcon size={iconSize} />,
    isShow: true,
    isExternal: false,
    eventName: 'Pages: Blog',
    type: 'Pages',
  },
  {
    title: 'Weekly',
    href: '/weekly',
    icon: <WeeklyIcon size={iconSize} />,
    isShow: true,
    isExternal: false,
    eventName: 'Pages: Weekly',
    type: 'Pages',
  },
  {
    title: 'Cheatsheet',
    href: '/cheatsheet',
    icon: <SnippetsIcon size={iconSize} />,
    isShow: true,
    isExternal: false,
    eventName: 'Pages: Snippets',
    type: 'Pages',
  },
  {
    title: 'Learn',
    href: '/learn',
    icon: <LearnIcon size={iconSize} />,
    isShow: true,
    isExternal: false,
    eventName: 'Pages: Learn',
    type: 'Pages',
  },
  {
    title: 'About',
    href: '/about',
    icon: <ProfileIcon size={iconSize} />,
    isShow: true,
    isExternal: false,
    eventName: 'Pages: About',
    type: 'Pages',
  },
  {
    title: 'Contact',
    href: '/contact',
    icon: <ContactIcon size={iconSize} />,
    isShow: true,
    isExternal: false,
    eventName: 'Pages: Contact',
    type: 'Pages',
  },
  {
    title: 'Playground',
    href: '/playground',
    icon: <SiJavascript size={iconSize} />,
    isShow: true,
    isExternal: false,
    eventName: 'Pages: Playground',
    type: 'Pages',
  },
];

export const SOCIAL_MEDIA: MenuItemProps[] = [
  {
    title: 'Email',
    href: `mailto:${socialAccounts.email}`,
    icon: <EmailIcon size={iconSize} />,
    isShow: true,
    isExternal: true,
    eventName: 'Contact: Email',
    className: 'bg-green-600! border border dark:border-neutral-700',
    type: 'Link',
  },

  {
    title: 'Linkedin',
    href: socialAccounts.linkedin,
    icon: <LinkedinIcon size={iconSize} />,
    isShow: true,
    isExternal: true,
    eventName: 'Social: Linkedin',
    className: 'bg-blue-500! border border dark:border-neutral-700',
    type: 'Link',
  },
  {
    title: 'Twitter',
    href: socialAccounts.x,
    icon: <TwitterIcon size={iconSize} />,
    isShow: true,
    isExternal: true,
    eventName: 'Social: Twitter',
    className: 'bg-sky-500! border border dark:border-neutral-700',
    type: 'Link',
  },
  {
    title: 'Instagram',
    href: socialAccounts.instagram,
    icon: <InstagramIcon size={iconSize} />,
    isShow: true,
    isExternal: true,
    eventName: 'Social: Instagram',
    className: 'bg-orange-700! border border dark:border-neutral-700',
    type: 'Link',
  },
  {
    title: 'Github',
    href: socialAccounts.github,
    icon: <GithubIcon size={iconSize} />,
    isShow: true,
    isExternal: true,
    eventName: 'Social: Github',
    className: 'bg-black! border border dark:border-neutral-700',
    type: 'Link',
  },
].filter((item) => {
  return !!item.href;
});

export const EXTERNAL_LINKS: MenuItemProps[] = [
  {
    title: 'Analytics',
    href: siteMetadata.analytics.analyticsURL,
    icon: <AnalyticsIcon size={iconSize} />,
    isShow: !!siteMetadata.analytics.analyticsURL,
    isExternal: true,
    eventName: 'External Link: Analytics',
    type: 'Link',
  },
];
