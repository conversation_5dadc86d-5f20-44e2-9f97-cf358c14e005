---
id: 9
chapter_id: 2
chapter_title: 'Chapter 2 : Numbers'
title: 'Math'
category: 'JavaScript Fundamental'
language: 'JavaScript'
difficulty: 'Easy'
source: null
cover_url: 'https://aulianza.s3.ap-southeast-1.amazonaws.com/images/learn/javascript.webp'
source_url: null
created_at: '2023-09-17'
updated_at: '2023-09-17'
is_playground: true
is_comment: false
---

JavaScript offers a powerful tool for performing mathematical operations through the `Math` object. It's static, meaning you don't need to create an instance of it. You can directly use its methods and properties with `Math.property`.

Some of the important properties include:

- `Math.E` // returns <PERSON><PERSON><PERSON>'s number
- `Math.PI` // returns PI
- `Math.SQRT2` // returns the square root of 2
- `Math.SQRT1_2` // returns the square root of 1/2
- `Math.LN2` // returns the natural logarithm of 2
- `Math.LN10` // returns the natural logarithm of 10
- `Math.LOG2E` // returns base 2 logarithm of E
- `Math.LOG10E` // returns base 10 logarithm of E

Here are some examples of math methods:

- `Math.pow(3, 2);` // 9
- `Math.round(5.4);` // 5
- `Math.ceil(5.1);` // 6
- `Math.floor(5.9);` // 5
- `Math.trunc(5.2);` // 5
- `Math.sign(-5);` // -1
- `Math.sqrt(25);` // 5
- `Math.abs(-7.2);` // 7.2
- `Math.sin(Math.PI / 2);` // 1 (the sine of 90 degrees)
- `Math.cos(0);` // 1 (the cosine of 0 degrees)
- `Math.min(10, 150, 30, 20, -8, -200);` // -200
- `Math.max(10, 150, 30, 20, -8, -200);` // 150
- `Math.random();` // A random number between 0 and 1
- `Math.log(Math.E);` // 1
- `Math.log2(8);` // 3
- `Math.log10(1000);` // 3

These methods are accessed directly, and you provide the necessary arguments. For example, `Math.pow(2, 3)` returns 8, as it calculates 2 raised to the power of 3.

Here's a quick reference for some common methods:

| Method            | Description                                                               |
| ----------------- | ------------------------------------------------------------------------- |
| `abs(x)`          | Returns absolute value of x                                               |
| `acos(x)`         | Returns arccosine of x, in radians                                        |
| `acosh(x)`        | Returns hyperbolic arccosine of x                                         |
| `asin(x) `        | Returns arcsine of x, in radians                                          |
| `asinh(x)`        | Returns hyperbolic arcsine of x                                           |
| `atan(x)`         | Returns arctangent of x as a numeric value between -PI/2 and PI/2 radians |
| `atan2(y,x)`      | Returns arctangent of the quotient of its arguments                       |
| `atanh(x)`        | Returns hyperbolic arctangent of x                                        |
| `cbrt(x)`         | Returns cubic root of x                                                   |
| `ceil(x)`         | Returns rounded upwards to the nearest integer of x                       |
| `cos(x)`          | Returns cosine of x, in radians                                           |
| `cosh(x)`         | Returns hyperbolic cosine of x                                            |
| `exp(x)`          | Returns exponential value of x                                            |
| `floor(x)`        | Returns round downwards to the nearest integer of x                       |
| `log(x)`          | Returns natural logarithmetic of x                                        |
| `max(x,y,z,...n)` | Returns number with the highest value                                     |
| `min(x,y,z,...n)` | Returns number with the lowest value                                      |
| `pow(x,y)`        | Returns value of x to the power of y                                      |
| `random()`        | Returns number between 0 and 1                                            |
| `round(x)`        | Rounds number to the nearest x                                            |
| `sign(x)`         | Returns if x is negative, null or positive (-1,0,1)                       |
| `sin(x)`          | Returns sine of x, in radians                                             |
| `sinh(x)`         | Returns hyperbolic sine of x                                              |
| `sqrt(x)`         | Returns square root of x                                                  |
| `tan(x)`          | Returns tangent of an angle                                               |
| `tanh(x)`         | Returns hyperbolic tangent of x                                           |
| `trunc(x)`        | Returns integer part of a number (x)                                      |

These methods are accessed directly, and you provide the necessary arguments. For example, `Math.pow(2, 3)` returns 8, as it calculates 2 raised to the power of 3.
