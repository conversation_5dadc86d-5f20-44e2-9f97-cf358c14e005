---
id: 4
chapter_id: 1
chapter_title: 'Chapter 1 : Basics'
title: 'Types'
category: 'JavaScript Fundamental'
language: 'JavaScript'
difficulty: 'Easy'
source: null
cover_url: 'https://aulianza.s3.ap-southeast-1.amazonaws.com/images/learn/javascript.webp'
source_url: null
created_at: '2023-09-14'
updated_at: '2023-09-14'
is_playground: false
is_comment: false
---

Computers possess a remarkable ability to handle more than just simple numbers. This is where the concept of variable types becomes crucial. Variables can assume various types, and different programming languages support distinct sets.

Here are some of the most commonly used types:

- `Number`: These can be whole numbers (e.g., 1, -5, 100) or decimal values (e.g., 3.14, -2.5, 0.01). Notably, in JavaScript, there's no distinct separation between integers and decimal numbers; they are all treated as numbers.

- `String`: Strings represent sequences of characters, and they can be denoted by either single quotes (e.g., 'hello') or double quotes (e.g., "world").

- `Boolean`: Booleans stand for a true or false value, and they are typically represented as true or false, without the need for quotes.

- `Null`: This type signifies a null value, essentially meaning "no value at all." It is denoted as null, without quotes.

- `Undefined`: This type characterizes a value that hasn't been set yet. If a variable has been declared but hasn't been assigned a value, it falls under this category.

- `Object`: Objects are collections of properties, each having a name and a corresponding value. You can establish an object using curly braces ({}), assigning properties through name-value pairs.

- `Array`: An array is a unique form of an object that can contain a series of elements. To create an array, you use square brackets ([]), populating it with a list of values.

- `Function`: Functions are blocks of code that can be defined and subsequently invoked by name. They can take arguments as inputs and return a value as output. You form a function using the function keyword.

JavaScript operates as a "loosely typed" language, meaning you're not required to explicitly declare the data type of variables. Simply using the var keyword to declare a variable is sufficient. The interpreter deduces the data type from the context and the use of quotes.
