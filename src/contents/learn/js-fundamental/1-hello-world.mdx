---
id: 1
chapter_id: 1
chapter_title: 'Chapter 1 : Basics'
title: 'Hello World'
category: 'JavaScript Fundamental'
language: 'JavaScript'
difficulty: 'Easy'
source: null
cover_url: 'https://aulianza.s3.ap-southeast-1.amazonaws.com/images/learn/javascript.webp'
source_url: null
created_at: '2023-09-12'
updated_at: '2023-09-12'
is_playground: true
is_comment: false
---

This is the initial lesson on first chapter, we're going to delve into the fundamentals of programming and acquaint ourselves with the JavaScript language.

Programming, in essence, involves crafting code. Much like how a book comprises chapters, paragraphs, sentences, and words, a program can be dissected into increasingly smaller components.

At this stage, one of the most pivotal components is a statement. Think of a statement as akin to a sentence in a book. While it holds its own structure and purpose, without the context of the surrounding statements, it may not convey much meaning.

A statement is more informally (and frequently) referred to as a line of code. This is because statements are typically written on separate lines. Consequently, programs are typically read from top to bottom, left to right. You might be curious about what code (also known as source code) entails. It's actually quite a broad term, encompassing both the entirety of the program and its smallest elements. Hence, a line of code is essentially one line of your program.

To illustrate, consider this straightforward example:

```
let hello = "Hello";
let world = "World";


// Message equals "Hello World"
let message = hello + " " + world;
```

This code can be executed by another program known as an interpreter. The interpreter reads the code and carries out all the statements in the correct sequence.
