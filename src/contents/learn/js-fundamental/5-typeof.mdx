---
id: 5
chapter_id: 1
chapter_title: 'Chapter 1 : Basics'
title: 'Type of'
category: 'JavaScript Fundamental'
language: 'JavaScript'
difficulty: 'Easy'
source: null
cover_url: 'https://aulianza.s3.ap-southeast-1.amazonaws.com/images/learn/javascript.webp'
source_url: null
created_at: '2023-09-14'
updated_at: '2023-09-14'
is_playground: false
is_comment: false
---

The `typeof` operator is a powerful tool used to determine the data type of a variable.

For instance:

- `typeof "John"` will return "string".
- `typeof 3.14` will return "number".
- `typeof NaN` will also return "number".
- `typeof false` will return "boolean".
- `typeof [1,2,3,4]` will return "object".
- `typeof {name:'John', age:34}` will return "object".
- `typeof new Date()` will return "object".
- `typeof function () {}` will return "function".
- `typeof myCar` will return "undefined".
- `typeof null` will return "object".

JavaScript's data types can be divided into two main categories based on the values they hold:

**Data types that can contain values:**

- `string`
- `number`
- `boolean`
- `object`
- `function`

Java<PERSON> provides several object types including `Object`, `Date`, `Array`, `String`, `Number`, and `Boolean`.

**Data types that cannot contain values:**

- `null`
- `undefined`

A primitive data value is a straightforward value with no extra properties or methods and is not an object. They are unchangeable, meaning they can't be modified. There are seven primitive data types:

- `string`
- `number`
- `bigint`
- `boolean`
- `undefined`
- `symbol`
- `null`
