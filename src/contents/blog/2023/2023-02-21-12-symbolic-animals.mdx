---
title: 十二生肖
slug: 12-symbolic-animals
date: '2023-02-21'
summary: '如何快速计算出今年是什么生肖呢？'
draft: false
tags:
  - JavaScript
  - ICON
  - SVG
featured_image_url: 'https://static.webjam.cn/images/202402/12-symbolic-animals.webp'
---

![十二生肖](https://static.webjam.cn/images/202402/12-symbolic-animals.webp)

_图片来源：[51miz.com](https://img.51miz.com/Element/00/90/65/71/4d52cf33_E906571_7229f133.png)_

## 如何计算某一年是什么生肖？

我们都知道十二生肖是有固定顺序的：一鼠二牛三虎四兔……，所以用数组结构再合适不过了，但是为了便于计算，数组并没有从口诀「一鼠二牛三虎四兔」的次序开始，而是经过微调。

微调之后直接用年份对 `12` 取余数，这个余数就是这一年的生肖在数组中的索引值：

```js
var animals = [
  '猴',
  '鸡',
  '狗',
  '猪',
  '鼠',
  '牛',
  '虎',
  '兔',
  '龙',
  '蛇',
  '马',
  '羊',
];
animals[2023 % 12]; // 兔
```

## 十二生肖中英对照

顺带着把十二生肖对应的英文单词也整理了一下：

| 中文 | 英文   | 中文 | 英文   | 中文 | 英文    |
| ---- | ------ | ---- | ------ | ---- | ------- |
| 老鼠 | rat    | 龙   | dragon | 猴子 | monkey  |
| 公牛 | ox     | 蛇   | snake  | 公鸡 | rooster |
| 老虎 | tiger  | 马   | horse  | 狗   | dog     |
| 兔子 | rabbit | 山羊 | goat   | 猪   | pig     |

## 十二生肖 SVG 图标整理

图标的 ID 就是上面的中英对照表里的英文字段。

你可以查看预览 codepen: [https://codepen.io/zhang-wc/pen/ExOYYap](https://codepen.io/zhang-wc/pen/ExOYYap)

下面是具体的代码，TLDR

```html
<svg xmlns="http://www.w3.org/2000/svg" style="display:none">
  <symbol id="rat" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M675 908q7-10 17-29 10-18 28-69 17-52 15-98t-35-102q-32-56-106-95-54-15-69-8-5 3-6 8 0 5 18 39 17 33 36 74 20 41 25 85t-15 72q-47 0-88-35-21-18-34-46-1-2-4-4t-11-10q-7-8-17-9t-22 0q-13 1-28 12-88 69-76 98 2 5 8 18l25 50q6 13 7 20t-1 11q-3 2-58 4-56 2-70 9-26 16-7 33 25 22 131 30 46 4 75 0 28-4 39-14 10-10 13-21 3-10 3-25 0-16 4-22 12-21 27-19 14 2 19 23 1 5 1 15t2 17q2 6 5 18 3 10 10 15 6 5 18 10t29 5q24 0 68 6 44 7 72 6t37-12q20-18-28-44-34-16-57-16zm236-249q-11-30-24-55-14-26-26-40-8-9-23-27-12-12-19-18t-11-16q-4-9-4-16t8-21q14-19 43-55 39-57 22-62-3 1-7 2t-15 8-20 12q-10 5-23 17-13 11-22 20-10 9-20 25-10 15-13 27-3 13-2 30 1 18 11 34 13 24 39 53 15 17 44 49 18 21 29 44 10 24 3 53-8 28-43 61-19 19-95 24-2 1-4 3t-5 9q-1 3-4 10-1 3 5 10t18 5q16 1 40-3 23-4 52-16 28-12 48-34 19-21 28-53 2-10 2-22 0-25-12-58zM330 322q30 13 52 4l4-2q18-9 27-25 24-53 0-73-14-13-29-18-15-4-26-2-10 2-19 7-9 4-14 8-1 1-3 5-12 14-17 32-5 17 1 37 6 19 24 27zm-34 241q-46 90-66 116-10 14-17 36-7 23-6 43 1 21 15 25 8 3 20-2 11-5 22-13 11-9 26-23 9-9 26-27 13-12 28-30 22-26 44-34 23-8 41-2t37 29q87 110 106 79 7-13 2-49-3-23-8-46-5-22-11-41-6-18-13-33l-15-27q-7-13-13-22-6-8-11-18t-8-12q0-4 1-10t4-19q3-14 3-28v-26q0-14-5-24-1-1-2-3 6 1 14 1t15 2q31 2 54-6 22-8 32-25 11-16 15-32 4-17 3-32t-5-25q-22-48-99-54-3 0-7-1t-15 1-21 3q-9 1-20 12-11 12-17 22-21 46-9 84-81-35-166-10-57 17-80 39-22 21-24 42-12 0-16 2-17 4-27 11t-12 15q-1 2-1 4 0 6 7 15 21 22 62 18 2 3 5 8t16 16q12 10 23 21t33 19q22 7 40 11zm35-164q7 4 13 7t12 10q-10 10-25 18h-1q0-1-2-1t-4-1-7 0q-4 1-7-1t-8-1-9 0q20-41 38-31z"
    />
  </symbol>
  <symbol id="ox" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M910 658q-6-7-26-26-21-18-39-40-19-21-27-41-13-32-48-55-35-22-45-12-7 4-18 5-20 3-138-4-1 0-4-1t-9 0h-12q-6 0-13 2-4 1-13 4-3 1-5 2-3 2-5 5-3 5-6 13 0 5 2 15 1 9-2 35-3 25-10 47-8 21-31 43-24 21-54 33-53 16-99 17t-64-3q-19-4-19-7-3 35-9 104-4 78 9 86 11 6 28-18 17-25 39-64 9-16 26-48 16 2 62 8-1 4-2 14-2 18-7 72-2 18-2 35 0 18 3 21 8 4 22 3 15-1 34-36t25-94q2 1 5 1 5 0 14 1 11 1 22 1 10 0 25 2 16 2 28 1t27 2q16 3 25 1t18 0q10 2 12 1-1 18-2 55 3 50 21 49 8 0 11-19 4-20 6-49 2-30 3-38 40-6 62-11v7q0 4-1 15t0 22q1 10 0 24-1 15 2 24t3 17 5 11 14-4q8-6 20-25 12-20 21-50 8-31 13-80t-1-113q1 0 4 1 7 6 16 29 4 10 8 16 3 6 7 19 4 12 10 18t12 15q6 10 14 11 5 0 15 2 8 1 18-6 0 1 1-5l3-12q2-7 4-15v-6q0-6-2-12-2-9-6-15zm-670 8q24 3 47 5 41 6 72 2 16-2 30-7 40-13 62-39 2-1 6-4 3-2 6-6 2-2 5-6 2-2 5-8 8-17 9-32t0-29q-1-13-10-25-9-13-11-20t-10-14q-1 0-1-1-1-27-1-44 18-3 29-12 11-10 12-18 0-5-15-16-9-7-29-21l-15-10q0-13-5-32-5-18-17-27-6-6-12-6t-8 3l-3 3q-4 54-93 59-20 1-34-13-14-15-16-31-1-5-2-15-35 0-60 65 0 2-2 5-9 1-19 4-10 4-19 8-5 2-15 8-7 4-10 7l-4 2q7 12 23 24 17 11 28 17t12 4 3 1 3 0q-10 34-20 52-26 10-39 28t-8 50q4 24 16 40t28 27q15 10 27 13 8 2 23 6 8 2 16 3t16 0zm-18-57q-8-8-7-29 1-20 19-15 24 7 5 44-8 2-17 0zm143-148q1 0 2 1t0 1q-1 7-3 19-3-1-12-2-19-5-12-18 7-12 20-6zm-21 104q18-24 26 15 0 5 2 15-5 12-19 17 0-1-2-2-11-5-14-18-3-12 7-27zm-76-85q-3 4-25 7-4-12-4-24 5 0 9-1 15-5 20 4 5 8 0 14z"
    />
  </symbol>
  <symbol id="tiger" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M200 729q35-36 140-142 1 0 3 1 7 4 19 11 4 2 13 8 3 2 12 9 5 4 10 10t5 11q4 21 23 35 25 21 28-23v-4q1-6 7-23-7 3-28 10l-2-3q-2-1-6-6-2-3-7-9-3-4-5-11t-1-12q1-4 7-10t16-8q16-3 27 6t21 26q11 16 10 17 3 4-2 11-4 8-12 14-2 1-6 5-1 0-1 1t-1 4-1 5 1 5 3 3q3 1 8 1 6 1 12-2 81-25 31-102-2-4-5-8-2-3-5-7-2-4-5-7-10-18-24-39-13-20-29-33-9-7-19-14-7-4-21-13 24-8 36-11 20-5 10-19-10-13-34-15-26-3-52 30-37-12-74-4-22 4-44 15-8 4-16 9-13 8-24 17-18 14-33 33-8 12-25 35-22 40-33 92-11 53 0 95 2 8 7 25 3 10 6 18 3 7 3 8t2 5 0 3-1 4q-2 0-4 1t-6 2-9 5-7 8q-2 5-4 11t3 13 13 15q3-5 8-15 10-11 14 12 0 6 1 17l12-12q6-5 11-8t11-4q7-1 10 2t4 14q4-1 12-5 7-3 23-8 15-5 21-4-52-50-57-101zm251-216q0-1 4 5 3 5 11 16 1 0 1 3t-1 5-1 5q0 4-4-1-3-1-6-3-2-1-4-3v-27zM143 787q-1 0-1 1l-1 1q1 0 2-2zm162-147q67 96 86 122v13q-33-11-38 8-4 13-3 26 1 12 9 14 8-7 23-22 2 7 9 28 7-6 26-24 4 3 16 11 51-24 55-38-49-59-149-175-9 9-34 37zm252-42q1 22-27 70-14 3-25 9-11 7-19 13-7 6-10 13t-5 14q-2 8-2 13t-1 9v3q5-2 20-6 2 4 6 17 6-5 27-22 4 3 14 13 5-3 22-12v-33q12-4 22-12 9-8 13-16 3-5 8-15 4-8 4-12 1-2 1-4 10-13 40-53-14-4-28-4-13 0-24 1t-19 5q-7 5-13 6-1 1-4 3zm367 110q-3-14-18-59-14-45-15-60-2-16-27-27-30-15-39 10-2 6-1 17-3-8-8-19t-19-36q-15-24-25-28-24-11-86-27-61-17-91-17-20 0 2-16 40-29 99-37 36-5 60 5 26 11 41 23 14 12 17 23 3 12-3 24t-19 22q9 11 29 32 2-2 6-7t15-18q10-14 14-28t3-34q-1-19-12-37-14-21-39-32-25-12-53-14-29-2-58 4-28 6-59 16-15 5-28 10-12 5-20 10-5 4-16 10-7 6-10 10-2 3-7 9-4 5-5 7t-5 5q-25-2-76-5 6 4 15 13 10 10 30 42 21 33 35 74 13-1 38-2 22-1 69 2 48 3 62 10 29 16 56 50 28 35 25 56-2 0-4 1-4 1-11 4-8 3-13 8t-12 11-8 14q-1 7 1 16t13 21v-4l2-7q1-2 3-8 1-3 6-6t12-3q2 29 11 37 17-29 28-30 3 9 8 27 3 7 7 7 4-28 18-28 10 0 24-3 13-3 27-13 12-8 12-19-1-3-1-6zM181 480h-3q-3 0-9-3t-11-8q-5-6-10-16-4-10-6-24-3-35 2-46 6-12 23-14 2 1 5 3t6 13q3 10 2 25-1 11 9 11 18-1 36-24 19-24 4-42-11-15-28-24-18-8-35-10-18-2-34 15-16 18-22 46t-6 51q0 35 14 58 10 17 24 17 17-1 39-28z"
    />
  </symbol>
  <symbol id="rabbit" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M655 927q1-3 11-8 9-5 18-15t8-27q1-34-53-30-2 1-5 1-13-24 4-41 8 4 19 7 12 3 20 6 3 0 8 1 21-7 2-52-18-45-36-58-4-2-9-6-3-2-9-7-4-3-7-8t-6-7q-4-2-7-7-1-2-5-6 30-13 44-38 15-24 15-49 0-24-5-47-6-22-11-37-2-4-7-13 22-45 37-84t19-66q3-16 8-49 3-22-1-35-5-14-6-24t-6-12q-1-2-3-4-32 7-59 35-28 29-44 64-17 35-28 69-11 33-17 57-2 7-5 22h-35q1-3 4-15 18-42 29-80 10-38 12-61 2-24-1-45-3-22-9-32-7-10-12-21-5-10-10-10-2-1-5-3-39 28-65 67-25 39-34 75-10 37-13 69t0 53q3 22 5 23t3 5q0 2 2 7 1 3 1 7t1 7v3q-24 19-35 39-11 21-11 38 0 18 7 33t14 27q7 11 17 19 11 8 10 11 4 6 1 16h-1q-10 4-38 16-62 29-72 71-8 27 12 37 11 4 24-2 49-20 51-10-2 19-7 55-80-21-83 16-2 23 22 42 5 3 10 5 28 14 6 19-90 6-89 33v2q2 10 10 17 27 6 52 8 26 2 45 2 20 0 37-2 18-2 31-6 8-2 25-7 11-3 18-8 7-6 12-8t7-6q1-1 2-3-16-20-14-31 2-10 10-14 9-4 15-1 34 13 43 27 3 4-2 5-7 2-21 5-7 2-9 5t-3 6 4 7 16 9q77 29 202-10l9-3q-16-39-84-40zM457 581v-1q-4-7 4-23l9-3q5 0 7 3t2 7-1 9-2 6q-2 4-6 10-10-2-13-8zm86-24q1-2 5-4t6-2q7 2 11 10-4 23-16 21-3-1-4-4-1-2-2-6-1-3-1-7t1-6v-2zm-13 76q-1 7-4 11t-6 7h-31q-14 0-13-8 1-7 21-14h2q24-7 31 4z"
    />
  </symbol>
  <symbol id="dragon" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M673 556q-10 0-31 7-20 7-39 14-18 7-30 5-5 0-11-3t-9-9-9-13q-6-8-8-15-2-4-6-13 8 0 20-2t16-2h13q4 2 13 9t14 12q3 3 9 10 5 1 17 2t22-11q11-11 3-30-8-23-2-38l15 15q2-5 8-19 10 3 42 12-1-4-5-17 5 0 8-2t4-6l12 6q0-22-21-39 20-4 23-9 3-4 4-9t-1-11-14-8q4-2 7-5t7-5q2-2 5-7 2 1 5 1t7-2 4-6q5-14-20-22 11-14 31-41 11-19 11-26 0-3-2-4t-6-2-17 5-26 22q-1 0-2-1t-4-1h-9q-4 0-4 2-19 15-26 35-10-2-31-8 2-1 6-6 3-4 12-14t14-20q5-9 13-18 7-9 5-13 0-13-14-13-23-1-46 35-7 0-9-14t-13 6q-1 2-2 5-4-15-11-28-6-12-14-21t-16-16q-8-8-18-14-9-6-17-8t-16-6q-9-4-14-3t-8-1q-1-1-3-1 4 14 15 57-12 1-49 2 1 2 1 6t3 14q3 9 4 18t1 15q0 7-1 10t-53 7q-22 2-4 7 2 1 7 2 14 4 28 14 13 11 18 18 2 2 6 7 5 15 2 28-3 12-18 22t-33 19q-17 8-43 12-25 4-48 9-22 5-48 5t-44 7q-19 7-25 16-6 8-11 16-3 5-3 9 0 2 1 3 0 2 1 6 5 0 22-2 3 7 12 27 4-5 15-21 6 5 22 21 2-5 7-11 3-4 9-11 4-6 10-10t9-8q0-1 2-2 3 7 13 22 10 14 19 23 3 4 9 10 2 25 12 47 10 23 20 36 11 14 23 26t18 15q3 2 8 5-4 2-8 9-4 6-6 13t-3 15-3 16q-2 7-1 14t-1 9v3q-68 36-49 80 1 3 5 8-4-10 3-24t16 2q7 10 12 12t7 2q1-1 3-2 5-12 8-16t5-3q3 1 8 5 7 4 14 6 12 3 15-12 3-16 0-29-1-6-2-16-2-15 12-33 15-19 33-26t37 16q28 31 1 75t-99 78q-92 44-29 75-15 19-21 33-5 13-4 19t9 6q7 0 14-1t16-5q7-3 20-9 6 25 18 25 15 0 39-37 11-19 11-20 10 14 18 19 8 4 16-5 8-8 13-15 6-7 11-22 2-7 6-20 0-2 2-8 28 15 33-33 3-23 1-50 36-20 45-77 9-56 2-103-4-23 0-38t9-22q2-1 6-5 9 5 36 21v-33q8 3 33 12l-8-40q6-2 25-11-37-28-59-18-23 9-32 23-9 15-10 20zm-7-155h-1q13-6 23 3-3 2-9 7h-11q-13-3-2-10zm-49-13q8 0 12 3t5 8q-2 1-6 4-3 2-7 3-5 1-5 0t-2-5q0-8 3-13z"
    />
  </symbol>
  <symbol id="snake" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M785 689q-24-12-47-19 31-114 2-197-22-64-82-126-59-63-130-104-70-41-126-44-8-1-16-1-23 0-44 6-29 9-50 20-22 11-35 25-7 8-23 24-1 2-7 8l80 68q-3 1-5 1-20 3-37 0-16-3-27-6-4-2-11-4-26-8-42-7-15 1-22 6-3 1-7 4 77 14 15 38-13 5-27 8 15 4 29 3t28-4q13-3 21-8 8-6 16-9 1-1 5-4 17-1 70-5 4 7 17 29-20 14-78 57 29 32 80 47 51 16 89-4 12-6 36-17 17-8 43-15 25-6 46-7 20-1 41 11 20 13 33 34 9 20 14 34t3 34q-2 19-6 36-4 16-15 47-48-1-89 13t-73 43q-33 29-59 64-25 34-53 83-13 23-31 37-17 15-33 20-17 5-37 4-21-1-32-5-9-4-28-10-16-7-19-10t-7-5q2 3 6 7 5 6 16 16 12 11 30 21 17 11 42 20 24 9 56 14 29 4 54-6 26-10 44-35 19-24 34-46 16-21 34-51t32-39q10-10 23-17 14-8 27-10t28-5q-7 24-21 71-10 36-12 64-2 29 0 51 2 21 13 35 11 15 22 25 10 10 30 14 21 4 34 9 9 2 23 2h15q25-2 38-2t37-4q15-2 32-9 16-7 32-20 17-14 30-29t24-36q10-20 17-40 7-19 7-42 0-22-5-43-5-20-20-41-16-20-36-37-21-16-56-30zM404 322q-12-2-22-1-4-13 4-25 8-13 26-12 25 1 27 19 2 14-18 18-8 2-17 1zm361 567q-30 15-48-11-19-25-12-78 1-13 5-31 8 2 24 7 34 11 48 35 15 23 10 47-6 23-27 31z"
    />
  </symbol>
  <symbol id="horse" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M455 526q-1-23-23-28 0-21 10-21 8 0-9-10-27-18-45-18-14 0-2-15 9-13 18-13 9-1-17-16t-39-15q-15 1-5-15 7-11 15-13 7-1-10-9-38-22-51-21 0 3 4 22 4 18 15 49 12 31 28 58 16 28 46 48 30 19 65 17zm440 320q-8-3-18-3-11 0-17-1t-9-3-5-10-10-26q-9-17-22-35-13-19-23-34-11-16-18-19-6-3-12-23-6-18-17-52-10-32-20-52-5-9-13-15-9-6-19-10t-25-7q-16-3-31-4t-37-1q-21 0-43-2h-48q-29 0-53-1-42-1-76-27-34-27-54-61-21-33-33-72t-19-61q-6-21-5-25-1-5-10-7-5 1-8 10t-4 18-2 9q-1 3-27 21-62 20-87 59-12 13-18 33-7 21-1 37 5 16 20 19t31-5q15-8 23-18 3-4 9-10 10 3 24 0 14-4 24-8 3-1 9-4 16 3 24 35 9 32 10 64v30q-3 5-9 21-3 11-11 35-6 19-10 36-4 18-8 30-2 4-4 12-15 12-44 36-30 25-53 48-22 24-27 27-7 9-10 16-4 8-5 20v1q0 11 15 15 21 10 38 1 16-8 27-24 12-17 17-30t5-18q-1-2 2-3t6-4q3-2 7-1t7-1h2q21-3 42-13 22-10 37-21 16-12 29-25t19-20q3-3 8-9 27 22 87 23 59 1 101-7t46-11q81 57 171 68 13 41 60 87 45 43 63 43h2q10-1 15-17 3-9 3-18 0-8-2-15-4-16-16-21zM237 402q-4 4-11 2t-11-5-9-8q4-8 11-13h4q13 3 18 10 4 7-2 14zm590 184q1 1 3 4l7 7q2 1 8 7 6 5 11 6t12 5q17 2 18-16 1-19-14-24-10-3-11-20-1-18-2-20-3-3-17-1-8 1-25 3-10 1-17-5-2-1-4-6t-4-6-7 0q-4 1-11 6-10 6-25 12-9 4-26 10-3 1-10 4-2 17 15 25t39 8q21 0 40 1 18 1 20 0z"
    />
  </symbol>
  <symbol id="goat" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M296 643q8-2 15-10 7-7 14-16 2-4 8-11l28-40q1-1 3-2 0-1 1-2 13-19 23-35 33-56 26-89 21-7 29-21 9-13 7-22-1-4-4-10h-57q-1-1-5-3-3-1-9-5-4-1-10-5-5-1-6-12t-1-36q0-8-4-12t-7-2q-1 1-3 2l-14 14q-5 5-7 9-2 5-9 9l-6 6q-3 3-5 4 0 1-1 2h-40q-1-26-5-41t-9-17l-3-3q-8 3-16 11-7 8-10 18t-5 20q-1 5-4 16v6q-3 8-10 31-4 8-9 6-3 0-11-2-5-1-16-4-8-2-19-1-10 1-15 5t-8 10v3q0 8 4 15 5 8 14 14 10 6 15 8t10 6q5 20 14 39 9 20 22 43 12 24 20 44 18 42 36 54 18 11 39 6zm-11-40q-13-6-18-13-5-6-2-9 3-2 8-7 6-4 11-4t8-1q13 4 18 10-2 4-6 11-1 4-3 6t-4 3q-2 0-5 2-2 0-7 2zm48-149q6-3 10 1t6 9v6q0 2-1 6t-2 11q-12 8-21 1-3-5-2-18 1-14 10-16zm-96 7q8-17 22 10-1 7-3 11t-5 4h-7q-2 0-5 1-12-9-2-26zm7 184q6 67 38 66 17 0 26-16t11-35q1-5 2-15-17 15-36 15-20 0-30-7-4-3-11-8zm668-29q-1-1-2-4-1-4-5-12-4-10-13-16-8-5-23-15-13-9-34-13-37-10-104 1-66 10-109 3-17-2-50-6-85-16-142-63-31 74-60 95 3 4 10 11 6 6 16 20 11 14 11 22v9q1 9 1 16t-1 20q-1 12-4 24-1 9-4 26-2 13-9 27-4 9-12 26-24 41-36 85t-8 54q5 0 14-2 49-17 111-145 0-1 1-2h57q35 0 103-7 67-8 88-22 98 74 101 86 2 5-1 18-3 12-9 27-6 16-6 25-1 7-1 15t2 14q2 7 6 11t13 1 21-14q38-34 48-88 1-6 0-19t-8-41q-6-28-19-55-9-18-9-34 0-17 6-31t15-23q10-10 22-17t23-7zM466 931q2 3 10 10 17-11 49-151h-54q-1 35-5 141zm236-162v177q3-2 9-5 5-3 16-18 11-14 12-33 10-66 29-68-3-5-10-14-6-9-25-24-18-15-31-15z"
    />
  </symbol>
  <symbol id="monkey" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M780 440q43-11 58-30 12-7-2-24-3-5-10-6-13-13-24-33-11-21-21-43-6-27-6-40 0-5-10-15-10-9-26-17-15-8-36-11-8-2-17-2-12 0-23 3-50 11-90 44-39 33-37 76 2 29 9 48 7 18 12 23t16 11q-99 8-297 26-1 0-3 1t-5 6-1 11q30 116 17 160-16 56-27 59-6-4-16-8-11-5-38-6-28-1-52 14-28 14-41 49-9 22-9 44 0 13 3 27 9 35 34 51 30 14 63 12 32-2 57-19 25-18 22-44-5-20-17-31t-25-16q-14-4-28-3t-21 7q0 1-2 4-1 2 6 9 6 7 19 11 15 5 19 19 5 13-7 23-11 10-33 9-2 0-7-2t-14-9-15-18q-7-12-2-32 4-21 18-47 3-2 8-5t21-9q15-5 29-5 15 0 30 10 15 9 27 28 9 16 15 40 6 25 10 47 4 23 11 48 6 26 12 43 6 18 20 32 15 14 32 16 8 2 16 2 15 0 27-5 18-6 36-20 17-14 23-17 5-1 41-3 76 0 109-71 10-22 17-47 3 0 9 2 0 1 1 3t2 9q1 4 4 11 2 6 7 15 3 4 9 14 4 6 14 11 6 3 18 8 7 4 22 2 16-1 27-5 23-7 35-18t7-23q-5-11-38-15-45-5-45-11-1-3 16-9h25q13 0 19-7 7-6 14-14t2-24q-9-44-65-80-55-37-97-20-21 9-62 28-16 7-35 20-20 14-25 29-2 3-8 6-3-12-3-27 0-14 3-37 3-24 12-43 9-20 32-43 23-24 52-41 2 0 4 2 5-2 13-4 7-2 32-1 26 1 49 7 22 6 53 24 30 19 54 47 3-2 9-8-4-35-15-139 3 1 11 3 7 2 25 3 17 1 31 0 15-1 26-14 11-14 13-31-3 1-27 5-25 4-59 3-33-1-46-13zm-32-100q8-7 11-6 5 5 9 10-9 6-14 6t-6-2q-2-2 0-8zm-50 2q24-19 51-29-8 13-23 39 15 11 60 46-7 25-28 17-4-1-7-3t-6-6q-2-4-4-6t-5-7-4-8-5-8l-7-7q-3-3-9-5t-12-5q-15-8-1-18zM283 699q27-40 52-52-10 40-7 81-15-10-45-29zm65-93q-4-1-8-3t-7-8q-3-7-4-15-1-3-1-8-1-6-1-13v-14q0-6 1-14t4-14 9-14q6-7 12-11t20-7q13-3 26-3 14 0 35 2 1 0 3 1-60 53-89 121zm342-122q-13-2-27-1-15 1-29 5t-16 8q1-1 2-5t2-5q1-2 5-7 4-6 8-7t13-6q9-6 19-7 6-1 20-3 12-2 24-3 11-1 25-4 15-3 25-4-1 22-2 67-1-1-5-2-4-4-12-7-12-4-35-14-9-3-17-5z"
    />
  </symbol>
  <symbol id="rooster" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M182 522q24 25 55 0 12-11 26-12 15-1 24 2t8 3v-2q5-24 33-19 14 2 23-6t11-16q1-4 3-11 7-1 14-12t10-20q1-4 3-11-5 0-15-10l-23-23q-12-14-23-28-12-14-20-23-2-3-7-10-1-8-3-32-9-6-32-7-22-1-39-1-6 1-18 2-1 1-5 3-3 1-7 4-9 6-25 17-2 1-7 6-13-3-38-8 5 8 18 33-4 5-17 23 2 0 7-2 4-1 14-2 9-1 16 0 3 0 7 2t9 7q5 6 14 18-9-4-17-6-5-1-14-4-6-1-10-1h-7q-21 14-28 22-4 4-4 7t3 4q6 3 12 4t19 1q12 0 12 1 6 1 7 5 1 5-1 8-1 1-2 4-21 10-32 25-11 16-12 31t1 28q2 12 7 21t10 9q5-1 14-9 8-8 15-13t11-2zm62-199q-8 5-25 4-1-3 0-5t3-5 5-3 7-3q2 2 4 5 1 2 4 3t2 3v1zm51-51q5-10 21-40-8 4-34 16-5-7-18-30-5 7-22 30-8-4-30-16 3 10 11 40h72zm582 117q-156-25-200 78 0-3 1-6t2-12 5-17 7-20q3-11 14-21 10-10 17-21 7-10 25-17 17-7 30-13 14-7 40-6-7-3-18-6-12-3-42-3-31 0-55 16-23 15-47 58-23 43-28 114-59-11-177-32-14-9-54-35-8 19-32 75v14h25q1 2 1 5t4 13q3 5 8 17 5 8 13 17 8 10 16 18t23 11q14 3 29 4 16 1 37-7 22-8 45-23 3 4 9 11-2 2-9 8-6 5-25 15-20 10-41 14-22 3-56-10-33-12-67-39-1-2-2-7-1-4-3-15t1-21q-1 1-3 1h-5q-5 0-7-2t-5-8 1-15q-1-1-3-2t-8-2-11-1-14 5q-8 5-13 10-3-1-9-1t-24 4q-17 4-31 18-7-2-19-5-12-2-32-1-19 1-25 13-23 55 35 121 9 9 22 25 8 10 24 29 10 12 23 25 14 14 25 22t25 10q15 2 28-2t28-17q2 4 6 10t18 16q15 11 30 14t42-11q27-13 53-47h14q7 0 16-4t18-9q10-5 24-17 14-13 25-27 10-14 24-40t25-55l24-72q3-1 11-4 7-3 25-9 19-6 36-6t38 6q20 6 35 24 13 16 21 32t13 32q6 15 7 28 0 8 2 24 1 10-1 17-2 8-2 10 4-8 14-22 35-60 14-110-10-25-38-43-27-19-60-27t-66-5q-32 3-54 18 3-4 8-9 7-6 20-18 14-14 32-23 17-9 37-16 21-7 43-2 50 8 76 26 27 19 29 35 2-4 2-12 0-10-3-27-9-54-56-61zM451 830q-8-1-29-4-6 17-24 67-42 0-55 12-4 4 0 9 3 5 33 0 7-1 7 0t-5 6q-23 20-26 43-2 12 7 12 9-1 38-40 4-6 12-16h7q4 0 7 4t7 5 6 5q1 1 2 3-3-15 0-83h17q-4-5-6-10-3-5 1-10 0-1 1-3zm-108-6q-5-4-6-10t0-9q1-1 2-4l-28-4-24 68q-42 0-56 11-4 4-1 8 4 6 35 1 7-1 7 1t-6 7q-23 19-26 43-1 12 7 11 10 0 38-41 9-12 12-14 3-3 6 0 3 2 8 4t8 5q2 1 5 5l3 2v-84h16z"
    />
  </symbol>
  <symbol id="dog" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M346 553q-4 17-17 70 38-1 66-9 13-4 27-10 16-8 34-21 31-22 91-68 32-25 53-59 22-33 31-65t12-58q3-25 3-44 0-5-1-15 10-3 20-8 9-5 15-9 4-3 11-9 3-2 10-9-24-24-47-34-22-9-37-4-14 5-26 13-12 7-18 17-1 3-5 8h-20q-24-13-63-16t-64 0q-9 1-26 3-4-25-17-35t-29-8q-15 2-30 7-14 5-25 11-4 3-10 7 2 24 43 24-8 97-58 133-8-1-24-2-13-1-18-3h-6q-4-12-9-21-5-10-11-13-4-1-12-5-5-2-8-1h-3q-17 1-26 23-10 21-11 51v5q0 27 10 55 10 31 27 54 39 45 168 45zm99-222q1-11 6-16 4-5 11-8h2l1 1q5 3 8 11t3 16v8q-3 11-19 17-13-6-12-29zm-95-12q10-7 18-2 7 5 8 31 0 15-22 23h-5q-1-3-4-14-2-7-2-15 0-9 1-14t6-9zm-33 252q-17-1-68-5-24 34-25 53 0 3-1 8-1 6 0 11 1 4-1 9t1 10 3 8 5 8q6 6 8 4 2-1 12 0 10 2 15-3 25-9 37-34 12-26 13-48 1-7 1-21zm276-74l-9 12q-6 7-26 26-19 20-43 37-23 17-58 37-35 19-74 32-61 142-77 181-8 21-12 34-4 12-9 33-5 22-3 35 2 14 11 23 18 15 61-47 43-63 68-121l15 9q-7 12-16 31-9 18-24 56-14 38-10 54 8 35 29 36 33 1 67-92 7-18 21-55 0 2 2 7 1 4 6 18 5 15 12 26t22 26q14 14 33 23 1-5 4-16 2-9 6-18 4-8 10-18t13-20q8-9 17-17t21-11q13-3 27-4t32 6q-4 0-12 1t-25 6q-18 5-31 15-13 11-25 33-11 23-9 50h-3q-2 1-8 2l-12 4q-4 1-14 4-8 2-15 5t-14 5-12 6q-4 4-9 7t-3 7q0 6 1 10 1 5 6 9t16 8q10 4 29 5 20 1 47 1 28 0 70-5t97-10q36-5 44-45 9-40-4-82-12-42-27-68-5-8-23-30-17-21-38-45-20-23-44-54-23-31-43-57-19-27-36-57-16-29-19-48zm312 184q-8 9-23 19-16 10-30 18-10 6-29 17-14 8-23 10-3 2-8 5 11 4 20 22 9 17 10 29 1 13 3 15 8 2 13 2t7-1q1-1 3-1 60-27 60-98 0-9-1-19 0-6-2-18z"
    />
  </symbol>
  <symbol id="pig" viewBox="0 0 1024 1024" class="icon">
    <path
      d="M558 775q-7 17-4 28 3 12 15 13 9 2 19-8 10-11 14-28 4-18-6-36-9 0-25 12-9 10-13 19zm358-229q-4-7-8-10-2-2-6-5-2-2-6 0t-6 2q-2 1-4 4-1 2-4 7-1 1-1 3t2 6q2 3 9-1 10-4 10 7-1 14-13 16h-3q-3-20-38-48-53-44-153-46H562q-1 2-3 4t-3 11-4 16q-3 6 2 17t8 15q19 21 35 55 16 33 15 66v71q0 11 1 32 1 20 4 29 4 10 7 12l2 2 10 5q4 1 11-1t15-8q7-7 15-25t16-43q18 1 55 3v7q0 4 1 17 1 12 5 19t10 15q6 9 17 5 15-2 18-14t0-25q-3-14 1-28 5-13 9 11 1 10 3 31 1 9 2 11 1 6 8 9t18-1q10-4 13-14 4-8 4-27 0-18 5-43 5-24 22-50 8-10 9-39 1-28-4-35l18-9q14-8 13-21 0-7-4-15zM593 688q6-30 3-55-3-24-13-44-11-19-19-31-8-13-17-19-3-2-7-7-3-36-11-63-7-28-17-43-10-16-17-24-8-8-15-11l-6-3q8-53-18-58h-3q-3 0-7 1-5 2-10 5-5 4-9 9t-9 10-7 8-7 9q-35 0-141-2-3-11-8-18t-10-6q-2-1-4-1-7-1-12 1t-8 8-4 11-1 13q0 9 1 12t1 10q0 1 1 3h1v9q0 6 3 11-10 13-16 32-6 20-9 38-3 19-4 38-1 20-6 32-4 12-9 18-2 3-19 20-10 9-29 27-11 12-19 29-8 16-8 28v3q8 51 57 80 49 45 142 60 32 5 60 5 52 0 90-17 121-36 140-128zm-163 9q-2 0-3-1t-2-1-3-2-3-1q-5-10-2-30 3-19 15-14 13 4 19 9 5 5 3 11t-5 12q-4 5-11 10t-8 7zm-7-243q10 0 16 25-5 25-19 25-7-1-12-13v-1q-1-2-2-8-2-6 0-12 1-7 5-11t12-5zm-116-3q9 3 9 30-17 50-32 7-1-17 7-29 8-11 16-8zm-51 260q-5-10-8-23-3-14 2-24t19-9q15 1 20 10t1 18l-3 9h1q-15 21-32 19z"
    />
  </symbol>
</svg>

<div class="icon-wrapper">
  <svg class="icon-svg">
    <use xlink:href="#rat"></use>
  </svg>
  <svg class="icon-svg">
    <use xlink:href="#ox"></use>
  </svg>
  <svg class="icon-svg">
    <use xlink:href="#tiger"></use>
  </svg>
  <svg class="icon-svg">
    <use xlink:href="#rabbit"></use>
  </svg>
  <svg class="icon-svg">
    <use xlink:href="#dragon"></use>
  </svg>
  <svg class="icon-svg">
    <use xlink:href="#snake"></use>
  </svg>
  <svg class="icon-svg">
    <use xlink:href="#horse"></use>
  </svg>
  <svg class="icon-svg">
    <use xlink:href="#goat"></use>
  </svg>
  <svg class="icon-svg">
    <use xlink:href="#monkey"></use>
  </svg>
  <svg class="icon-svg">
    <use xlink:href="#rooster"></use>
  </svg>
  <svg class="icon-svg">
    <use xlink:href="#dog"></use>
  </svg>
  <svg class="icon-svg">
    <use xlink:href="#pig"></use>
  </svg>
</div>
```

```css
.icon-wrapper {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(2, 1fr);
}
.icon-svg {
  width: 60px;
  height: 60px;
}
```
