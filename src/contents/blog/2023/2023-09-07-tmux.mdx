---
title: '用了 Tmux 之后：真香'
slug: tmux
date: '2023-09-07'
draft: false
summary: 'Tmux 可以让我们的终端更加强大，也就是它是对终端功能的一种增强，非常适合需要打开非常多个终端窗口的程序员。'
featured_image_url: 'https://static.webjam.cn/images/202309/tmux-terminal2.webp'
tags:
  - Tmux
  - Terminal
---

## 开始之前

终端是我们程序员离不开的一个工具，而 Tmux 可以让我们的终端更加强大。

如果你像我一样，每天都要打开很多个终端窗口或 Tab，并且每次开机的时候都会因为需要重新打开这些终端窗口而苦恼，那么恭喜你，Tmux 能解君忧 👏。

相反如果你平常不太需要用到终端，或者能用到，但是打开一个或两个终端窗口就够用了，那么我建议粗浅了解一下 Tmux 的作用即可，把时间用在更有意义的事情上面，等你以后真的需要 Tmux 了再回过头来深入学习使用也不迟 😛。

![](https://static.webjam.cn/images/202309/tmux-terminal2.webp)

## Tmux 使用场景

上面简单提到了，就是管理非常多的终端窗口，举个例子 🌰，我作为前端开发，上班时间需要开发和维护三个项目，然后我学习其他框架或三方库又创建了两三个项目，同时我还有一些配置文件（比如 nginx 配置）或者脚本（升级 brew 脚本）需要经常编辑和查看。

这就带来一个问题，我该如何在这**十来个**路径中来回切换，一开始是 `cd` 来 `cd` 去再配合路径补全，后来用上了 zsh 的 [jump 插件](https://github.com/ohmyzsh/ohmyzsh/blob/master/plugins/jump/README.md)快速跳转，但是总还是有一些记忆负担，也就是我每次跳转之前必须先回忆一下要去什么路径，年纪越大项目越多，这个事情就愈发为难。

另外一个问题就是，如果我是启动了一个服务，那么这个终端窗口就不能关闭，如果关闭了窗口那么服务也随之关闭。那么这时候就会有两个选择，一是关闭服务然后跳转到另外一个路径，等下次切换过来的时候再启动服务，二是再打开一个窗口，在另外一个窗口里面操作，好在 iTerm2 支持 Tabs，可以像 Chrome 浏览器一样打开多个标签页，而且 iTerm2 还支持重命名标签页和对标签设置颜色，但是这个就限制了我必须使用 iTerm2，如果我切换到 Linux 环境或者再 Windows 电脑上目前我还没有什么好办法。

说了这么多，其实也可以看出来，上面的问题并不是解决不了，只是解决得不够优雅，不够爽！

而 Tmux 有点类似 iTerm 的多标签功能，你可以创建很多的「窗口」，每一个窗口对应一个项目，然后再给每个窗口设置名字以便于切换，切换的时候，其他窗口的程序仍然运行。

这一点就很棒，如果你有过登录服务器做一些工作的经验，应该有过这些体会：每次登录后都处于用户/root 目录；如果想要同时做一些操作，那就需要在另外一个窗口中再登录一次。如果你使用 Tmux 那么只需要登录一次，而且之前进入的路径和启动的服务也都还在。

同时 Tmux 本质上是一个终端程序，不像 iTerm2 只能再 Mac 上使用，Tmux 支持 MacOS 和 Linux，如果你是 Windows 电脑，可以在 WSL 中使用。

而且 Tmux 还有一个很重要的功能就是通过插件达到「保存会话」的功能，作用就是窗口你只需要创建一次，即使重启电脑了，之前创建的窗口也都能恢复。

最后一个使用 Tmux 的原因就是，它和 VIM 一样，都是可以迁移而且长久使用的技能/工具，尽量去学习这些能够长久为自己提供方便的东西。

## 概念理解

如果是一开始接触 Tmux，直接就去看那些非常正经的教程，会非常迷惑会话、窗口、窗格傻傻分不清，下面我按照自己的理解打一个比方帮助大家理解。

以现实中的纸质笔记本为例，创建一个会话就相当于是购买了一个新的笔记本，你可以为你每一个学科都买一个笔记本，分别记录不同学科的笔记，放在 Tmux 中就是我可以为每一个项目创建一个会话，在这个会话中我只做和这个项目相关的操作。

![](https://static.webjam.cn/images/202309/tmux-vs-notebook.webp)

当我拿到一个笔记本之后，显而易见的是我要从某一张纸开始记录，Tmux 中的窗口就是笔记本里面的一页纸，在笔记本中你可以向后翻页或者向前翻页，也可以直接翻到某一页，在 Tmux 中也是一样的，你可以切换窗口来看不同的内容。

但是有时候窗格就是在某一张纸上用铅笔垂直或者水平的画一条线把这张纸分成了几个格子，只要你愿意而且屏幕够大，你可以在 Tmux 的窗口里面画出无数个「格子」。

在 Tmux 中创建会话后会直接进入窗口（也就是「笔记本的第一页」），你可以直接在这个窗口中执行命令（相当于这页纸上开始「记录」），你也将当前窗口划分成多个窗格然后在不同的窗格中分别执行命令。另外窗口和窗格你可以随意创建和划分，也可以随时删除。

## 命令和快捷键

Tmux 的命令和快捷键确实不少，阮老师也总结得很好了：[Tmux 使用教程 - 阮一峰的网络日志 (ruanyifeng.com)](https://www.ruanyifeng.com/blog/2019/10/tmux.html)，如果你是新手直接看这篇教程就行。

如果你已经入门使用一段时间了，有时候需要查找一些命令，那么可以看我在阮老师教程基础上总结的 [tmux 思维导图](https://static.webjam.cn/images/mindmap/tmux-mindmap.png)：

![tmux 思维导图](https://static.webjam.cn/images/mindmap/tmux-mindmap.png)

## 个性化配置

Tmux 默认的快捷键前缀是 `Ctrl + b`，我改成了 `Ctrl + f`，因为这样手指移动很小的距离就可以按到这两个键，非常方便。

个性化的配置我是参考了向军大叔 [tmux | 后盾人 (houdunren.com)](https://doc.houdunren.com/%E6%95%88%E7%8E%87%E6%8F%90%E5%8D%87/10%20tmux.html#tmux)，我的建议是要有选择性的配置，看看那些是自己需要的，不要一股脑把别人的配置全搬过来，只有适合自己的才是最好的。

## 参考文章

- [Tmux 使用教程 - 阮一峰的网络日志 (ruanyifeng.com)](https://www.ruanyifeng.com/blog/2019/10/tmux.html)
- [tmux | 后盾人 (houdunren.com)](https://doc.houdunren.com/%E6%95%88%E7%8E%87%E6%8F%90%E5%8D%87/10%20tmux.html#tmux)
