---
title: '如何在局域网中把电脑设置成固定 IP'
slug: configure-static-ip-in-lan
date: '2023-08-30'
draft: false
summary: '如果想要在本地电脑上部署一些服务在局域网中使用，那把电脑设置成固定 IP 非常用帮助。'
featured_image_url: 'https://static.webjam.cn/images/logos/wifi.svg'
tags:
  - 局域网
  - 固定IP
---

如果想要在本地电脑上部署一些服务在局域网中使用，那把电脑设置成固定 IP 非常用帮助。

IP 格式内网 IP 都是这样的：

```txt
IP 地址: *************
IP 地址: ************86
IP 地址: ************87
IP 地址: ************88
IP 地址: ************89
```

简单（通俗）来讲，IP 由四个数字组成通过 `.` 连接起来，每个数字的取值范围是 `0 - 255`。

默认情况内网都是以 `192.168` 开头，接下来的 `13` 是由路由器控制，可以改成其他值，每一个接入此路由器网络的设备 IP 第三个数字都是 `13`，不同设备 IP 的第四个数字互不相同。

一般路由器都提供了固定 IP 的功能，我目前接触到了华为路由器和 TP-Link 路由器，下面以这两台路由器为例，分别我把的电脑 IP 都改为 `*************` 的固定 IP。

## 华为路由器如何设置

登录路由器管理端，然后打开局域网设置。路径是：更多功能 - 网络设置 - 局域网：

可以在 ① 处修改路由器的 ip 为 `************`，这个将会是以后访问路由器管理页面的网址，同时也确定了网段为 `13`，点击保存完成修改。

![](https://static.webjam.cn/images/202308/bg0830230408.webp)

然后在 ② 处点击加号为你的设备绑定静态 IP，此时会出现一个弹窗用来选择设备和输入 IP 地址。

_提示：选择设备的时候如果遇到重名设备，可以通过在「终端管理」页面查看当前联网的每一个设备的 MAC 地址来分辨。_

## TP-Link 如何设置

这是 TP-Link 路由器，首先在「LAN 口设置」中把设置改为手动，然后把 IP 修改为 `13` 段

![](https://static.webjam.cn/images/202308/bg0830230413.webp)

还记得我们的目标吗，我们希望把 `*************` 分配给自己的电脑。

然后在「DHCP 服务器」中检查「地址池开始地址」和「地址池结束地址」，这是局域网 IP 的分配范围，主要看最后一个数字，这里显示是 `10 - 199`，刚好包含了 `13`（`*************` 的最后一个数字），如果不包含的话，你需要手动修改一下分配范围使其形成包含关系。

![](https://static.webjam.cn/images/202308/bg0830230419.webp)

接下来在「应用管理」中找到「IP 与 MAC 绑定」点击进入：

![](https://static.webjam.cn/images/202308/bg0830232435.webp)

最后为你的设备设置一个静态 IP

![](https://static.webjam.cn/images/202308/bg0830232932.webp)

## 总结

1. 修改完成之后，一定要记得先断开网络（拔网线或者关闭 WIFI 连接）再重新连接，这样才能拿到分配到最新 IP 地址。
2. 检查自己局域网 IP 地址的方式，如果是 MacOS 我这里有一条命令拿去用：

```bash
/sbin/ifconfig -a | grep inet | grep -v 127.0.0.1 | grep -v inet6 | awk '{print $2}' | tr -d "addr:"
```

3. IP 地址中的第三段由路由器决定，**一旦更改会影响所有连接这台路由器的设备的内网 IP**，更改之前要想清楚有什么影响。具体的修改方式大同小异，TP-LINK 的入口是「LAN 口设置」，华为路由器的入口是「我要上网」，进入页面后都是先设置为「手动」然后输入自己想要的 IP，然后保存即可。
