---
title: 'An<PERSON> 同步问题的最新解决方法（2023年）'
slug: anki-latest-sync-solution
date: '2023-08-31'
draft: false
summary: 'Anki 从 `2.1.57+` 开始已经内置了一个同步服务器，这意味着你可以在电脑端直接启动一个同步服务，这下终于可以告别那一言难尽的原来 AnkiWeb 同步速度了。'
featured_image_url: 'https://static.webjam.cn/images/logos/anki.svg'
tags:
  - anki
---

Anki 从 `2.1.57+` 开始已经内置了一个同步服务器，这意味着你可以在电脑端直接启动一个同步服务，这下终于可以告别那一言难尽的原来 AnkiWeb 同步速度了。

这个同步功能需要你在 PC 电脑上启用，这里我介绍两种最简单的启用方法。

### 安装软件

首先，根据你的电脑系统安装 Anki（安装方式见[各平台客户端下载地址](#各平台客户端下载地址))，这个软件我估计大家早已经安装了，不过要注意在 `2.1.57` 之后的版本才有同步服务功能，所以请检查你的软件版本。

### 执行命令启动服务

然后如果你是 windows 系统，需要打开 `cmd.exe` 执行下面命令:

```bash
set SYNC_USER1=user:pass "\Program Files\anki\anki.exe" --syncserver
```

如果你是苹果系统，则打开终端 Terminal.app 执行下面命令:

```bash
SYNC_USER1=user:pass /Applications/Anki.app/Contents/MacOS/anki --syncserver
```

解释一下：`user` 和 `pass` 分别代表用户名和密码，一般看到这里的都是 Anki 老用户了，肯定在 AnkiWeb 注册过账号，但是一定要分清楚这是两个系统了，你之前在 AnkiWeb 中的账号和这里是不通用的。

所以执行上面命令之前需要把 `user` 和 `pass` 替换为你希望使用的用户名和密码，然后这个命令会创建一个新用户，然后你的各端 APP 就使用这个用户名和密码登录和同步。

另外要注意 Windows 用户需要把命令里面的路径改为你电脑上真实的安装路径，苹果系统的路径一般是不需要改动了，只需要替换上你想使用的用户名和密码即可。

### 可选项：自定义端口号

默认启用的服务在 `8080` 端口，这个端口在程序员电脑上的使用频率比较高，很容易发生冲突，所以我改成了 `27701` 端口，命令如下：

```bash
SYNC_USER1=zwc:123456 SYNC_PORT=27701 /Applications/Anki.app/Contents/MacOS/anki --syncserver
```

即加上 `SYNC_PORT=27701` 参数即可。

### 更多需求

如果你需要使用多个账户或者更多的需求，请查阅官方说明[Sync Server - Anki Manual](https://docs.ankiweb.net/sync-server.html)，里面有更多的启用服务方式，比如 Pip、Cargo 和源码启动。

### 各平台客户端下载地址：

- 电脑（PC 和 Mac）端：2.1.57 及以上。下载地址：[apps.ankiweb.net/downloads/archive/](https://apps.ankiweb.net/downloads/archive/)
- iOS（iPadOS）端：AnkiMobile 2.0.88 及以上
- Android（HarmonyOS）端：AnkiDroid v2.16alpha77 及以上。下载地址：[github.com/ankidroid/Anki-Android/tags](https://link.zhihu.com/?target=https%3A//github.com/ankidroid/Anki-Android/tags)

### 官方文档

官方文档：[Sync Server - Anki Manual](https://docs.ankiweb.net/sync-server.html)

## 总结

总结一下，你需要在电脑端安装 `2.1.57` 以上版本的 Anki，然后再命令行启动服务。

然后需要在各个 Anki 软件设置中填写 Anki 同步服务地址，然后你需要在同一个局域网（通俗讲就是连接同一个路由器，不管是网线连接还是 WIFI 连接）下才能同步成功。

### 常见问题

要注意，同步的时候要保证同步服务在运行中，同步失败的时候先想想是不是电脑已经关机了。因为当你电脑关机这个服务也关掉了，下次开机的时候要记得再次执行上面的命令。

如果电脑在开机状态，而且同步服务也启动了，但仍然同步失败，那你就要检查一下是不是电脑的 IP 发生变化了。这是因为默认情况下路由器给你的电脑分配的 IP 不是固定的，这种情况下只需要再 Anki 设置中把同步服务地址改成你新的 IP 就好了。

> 如果不想每次都重复「同步 - 失败 - 检查 IP - 更新地址」这一套麻烦的流程，还有一个解决方案 —— 设置电脑为静态 IP。
>
> [如何在局域网中把电脑设置成固定 IP](/blog/configure-static-ip-in-lan/)
