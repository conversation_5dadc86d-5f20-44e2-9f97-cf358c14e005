---
title: HTML <dialog> 元素完全指南：从基础到高级用法
slug: dialog
summary: '全新的个人网站, awesome!'
date: '2024-01-10'
featured: true
tags:
  - blog
draft: true
featured_image_url: https://static.webjam.cn/images/202401/aulianza.webp
---

### 题图：TODO

本文章写于 2025 年 1 月。  
预计阅读时间：5 分钟

---

### 这篇文章写什么？

如何在 HTML 中使用 `<dialog>` 元素，特别是如何关闭对话框，以及如何处理多个按钮的场景（如关闭和确定按钮）。

---

### 对谁有用？

前端开发者，尤其是需要在项目中实现对话框功能的开发者。

---

### 直入正题：如何使用 `<dialog>` 元素？

```html
<dialog>
  <p>你好, 这是一个弹窗</p>
</dialog>
```

是的，就这么简单，不过目前还不具备打开和关闭功能。

### 如何打开弹窗

#### `open` 属性

#### show() 方法

#### showModal() 方法

### 如何关闭弹窗

在 HTML5 中，`<dialog>` 元素提供了一种内置的对话框功能，**可以通过 JavaScript 关闭**。以下是几种常见的关闭方式：

---

#### **1. 使用** **`close()`** **方法**

如果 `dialog` 已经被打开（`showModal()` 或 `show()`），可以使用：

```ts
const dialog = document.getElementById('myDialog') as HTMLDialogElement;
dialog.close(); // 关闭对话框
```

📌 **适用场景**：对话框已经打开，手动触发关闭（如点击按钮）。

---

#### **2. 在 HTML 按钮上使用** **`formmethod="dialog"`**

如果 `dialog` 内部有 **表单** 或 **按钮**，可以使用：

```html
<dialog id="myDialog">
  <p>这是一个对话框</p>
  <button id="closeBtn">关闭</button>
</dialog>

<script>
  const dialog = document.getElementById('myDialog');
  const closeBtn = document.getElementById('closeBtn');

  closeBtn.addEventListener('click', () => dialog.close());
</script>
```

📌 **适用场景**：用户点击 **“关闭”** 按钮时关闭。

---

### 如何实现点击\*\* **`dialog`** **外部区域关闭**

对 `showModal()` 打开的 `dialog`，可以监听 `click` 事件：

```ts
const dialog = document.getElementById('myDialog') as HTMLDialogElement;

dialog.addEventListener('click', (event) => {
  if (event.target === dialog) {
    dialog.close();
  }
});
```

📌 **适用场景**：点击 `dialog` 外部关闭 **模态对话框**。

---

#### 如何实现 Esc 按键关闭\*\*

默认情况下，`<dialog>` **按** **`Esc`** **键可以自动关闭**，但如果想监听这个行为：

```ts
document.addEventListener('keydown', (event) => {
  if (event.key === 'Escape') {
    dialog.close();
  }
});
```

📌 **适用场景**：用户按 `Esc` 时手动控制 **关闭逻辑**。

---

## **总结**

| 关闭方式                              | 代码示例                                    | 适用场景                                   |
| ------------------------------------- | ------------------------------------------- | ------------------------------------------ |
| **JS 手动关闭**                       | `dialog.close();`                           | **常见方式，点击按钮触发**                 |
| **按钮\*\***`formmethod="dialog"`\*\* | `<button formmethod="dialog">`              | **适用于表单内按钮**                       |
| **点击外部区域关闭**                  | `event.target === dialog`                   | **点击\*\***`dialog`\***\*外部时自动关闭** |
| **Esc 键关闭**                        | `document.addEventListener("keydown", ...)` | **监听键盘事件**                           |

你希望在哪种情况下关闭 `dialog`？😊

---

## **进阶：如何处理多个按钮的场景**

如果你的 `dialog` 需要有两个按钮，一个用于关闭，另一个用于执行某些逻辑（如提交表单），可以这样实现：

---

### **1. 使用** **`form`** **传递** **`returnValue`**

`dialog` 元素有一个 `returnValue` 属性，可以用 `form` 传递：

```html
<dialog id="myDialog">
  <p>你确定要提交吗？</p>
  <form method="dialog">
    <button value="yes">确定</button>
    <button value="no">关闭</button>
  </form>
</dialog>

<button onclick="document.getElementById('myDialog').showModal()">
  打开对话框
</button>

<script>
  const dialog = document.getElementById('myDialog');

  dialog.addEventListener('close', () => {
    console.log('用户选择了：', dialog.returnValue);
    if (dialog.returnValue === 'yes') {
      // 执行确定按钮的逻辑
    } else {
      // 执行关闭按钮的逻辑
    }
  });
</script>
```

📌 **适用场景**：

- `button value="xxx"` **会自动设置** **`dialog.returnValue`**，并且关闭对话框。
- **监听** **`close`** **事件**，可以知道用户点击了哪个按钮。

---

### **2. 纯 HTML 方式（无 JS）**

如果不需要额外的逻辑，可以直接使用 `<form method="dialog">`：

```html
<dialog id="myDialog">
  <p>这是一个对话框</p>
  <form method="dialog">
    <button>关闭</button>
    <button type="submit">确定</button>
  </form>
</dialog>

<button onclick="document.getElementById('myDialog').showModal()">
  打开对话框
</button>
```

📌 **工作原理**：

- `form method="dialog"` **会自动关闭对话框**，无需 JS 处理。
- 适用于 **模态对话框**（`showModal()` 方式打开）。
- **不适用于非模态对话框**（`show()` 方式打开）。

---

### **3. 获取输入数据**

如果对话框包含输入框并需要提交数据：

```html
<dialog id="myDialog">
  <form method="dialog" id="myForm">
    <p>请输入您的姓名：</p>
    <input type="text" id="username" />
    <button>确定</button>
  </form>
</dialog>

<button onclick="document.getElementById('myDialog').showModal()">
  打开对话框
</button>

<script>
  const dialog = document.getElementById('myDialog');
  const form = document.getElementById('myForm');

  form.addEventListener('submit', () => {
    const name = document.getElementById('username').value;
    console.log('输入的姓名是：', name);
    // 执行确定按钮的逻辑
  });
</script>
```

📌 **适用场景**：

- **表单提交但不刷新页面**。
- 适合 **获取输入数据**，然后进行后续操作。

---

### 能否多个弹窗共存？

### 如何修改弹窗层级关系

## **层级关系**

`dialog` 永远在最上层（`dialog` 标签属于 `top-layer` 元素），因此没办法使用 `z-index` 来调整层级关系。

---

### **总结**

| 方式                  | 代码示例                         | 适用场景                   |
| --------------------- | -------------------------------- | -------------------------- |
| **直接关闭（无 JS）** | `<form method="dialog">`         | **普通关闭按钮**           |
| **传递 returnValue**  | `<button value="yes">`           | **需要知道用户选择了什么** |
| **获取输入数据**      | `<input>`+`form method="dialog"` | **提交表单数据**           |

如果你的需求只是 **点击按钮关闭**，直接用 `<form method="dialog">` 就够了 ✅。  
如果你需要 **获取用户的选择**，可以监听 `returnValue`。

---

### 全文完

感谢阅读！如果你有更多问题或想法，欢迎留言讨论。😊
