---
title: 配置 SSH Config 实现快速登录远程服务器
slug: ssh-config
summary: '配置 SSH Config 实现快速登录远程服务器'
date: '2025-02-28'
featured: true
tags:
  - ssh
  - ssh-copy-id
draft: true
---

## 这篇文章写什么？

这篇文件写通过配置 SSH Config 来实现快速登录远程服务器

## 前置条件

有一台远程服务器（虚拟机）

## 什么是 SSH 和 SSH Config

SSH 是一种登录远程服务器的方式，具体操作是执行下面这个命令：

```bash
ssh <user>@<ip>
```

使用的时候将 `<user>` 和 `<ip>` 替换为自己的用户名和 ip 地址，例如 `ssh root@*************`。

很明显这种方式存在的问题就是 ip 容易忘记或者记混，而 SSH Config 则是一个配置文件，可以将 SSH 相关信息(比如用户名和 ip 地址)记录下来，具体文件路径为 `~/.ssh/config`

## 如何配置

打开 `~/.ssh/config` 文件，增加下面格式的代码：

```config
Host <ServerName>
    Hostname <ip>
    User <user>
    Port 22
    ServerAliveInterval 180
```

`<ServerName>` 表示服务器名称，我们可以替换成比较方便记忆的单词，比如我有一台在阿里云买的服务器，就命名为 `ali`。

`<ip>` 就是服务器 IP，`<user>` 则是登录服务器的用户名，端口号一般是 22 一般不用修改，`ServerAliveInterval` 的作用是每隔 180 秒向服务器发送一个心跳，避免登录后长时间没有操作而自动端口连接。

例如我修改后的配置为：

```config
Host ali
    Hostname *************
    User root
    Port 22
    ServerAliveInterval 180
```

保存配置后，我就可以用更简单的命令来登录这台服务器了：

```bash
ssh ali
```

## 继续优化：免密登录

经过配置后，确实方便了很多，但还有一点不爽就是还需要输入密码。既然用户名和 IP 都能放到配置文件中，那么密码干嘛不也放在配置文件中，这样使用 `ssh ali` 的时候就不必输入密码了。

但是出于安全考虑，我们不应该明文保存密码，因此 ssh 提供了另外一种方式来实现免密登录：公钥和私钥。

其中公钥可以公开，放在服务器端，而私钥必须保密，保存在本地，用来解密公钥。

## 如何生成公钥和私钥

先查看是否已经存在公钥和私钥，如果已经存在则无需重新生成。

```bash
ls -l ~/.ssh/id_*.pub
```

这个命令是列出来所有公钥文件，如果返回结果，则存在公钥（当然也就存在私钥）。

如果没有公钥，则需要重新生成，执行下面命令：

```bash
ssh-keygen
```

生成过程中会询问几个问题，最简单的办法就是直接按回车一直按到结束。

最终生成的文件会存放 o 在 `~/.ssh/` 目录，如果上面问题中选择了 ras 算法，就会生成公钥 `~/.ssh/id_rsa.pub` 和私钥 `~/.ssh/id_rsa`

注意带有 `.pub` 后缀的是公钥，这个文件可以公开，另外一个一定要保密。

## 有了公钥和私钥，如何配置免密登录

配置也相当简单：

```bash
ssh-copy-id ali
```

这个命令会自动将刚才生成的公钥文件内容添加到远程服务器的 `~/.ssh/authorized_keys` 文件的末尾。

因此，如果你不想用命令也可以手动复制公钥内容到远程服务器的 `~/.ssh/authorized_keys` 文件末尾，一定要记住另起一行才行。

至此，免密配置已经完成，你可以重新执行 `ssh ali` 来验证免密配置是否成功。

## 结束

文本结束，感谢阅读。
