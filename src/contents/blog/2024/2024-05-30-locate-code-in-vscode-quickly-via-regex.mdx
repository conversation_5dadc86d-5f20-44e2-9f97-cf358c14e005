---
title: 利用正则在 VSCode 中快速定位代码位置
slug: locate-code-in-vscode-quickly-via-regex
summary: 相信有过维护老项目经验的同学一定都经历过无法快速定位代码位置的痛苦。本文教你如何利用正则在vscode 中快速定位代码位置
date: '2024-05-31'
featured: true
tags:
  - vscode
  - 正则
draft: true
featured_image_url: https://static.webjam.cn/images/202405/vscode-search-01.webp
---

## 序

项目开发维护过程中难免需要对代码「修修补补」，那么如何快速找到对应的代码位置呢？

我更喜欢通过代码编辑器「全局搜索」的方式通过关键字查找，简单粗暴，搜索速度也非常快。按下快捷键 `commond + shift + f` （这是 VSCode 的快捷键）聚焦到全局搜索，然后输入关键字，不到一秒钟就找到了所有包含该关键字的文件，然后再配合文件路径和文件后缀过滤，很快就定位到具体的代码位置了。

这个方式唯一的缺点就是比较依赖关键字，如果关键词太短或者使用频率太高，有可能搜索结果会非常多，这样排查起来就比较慢，因此本文介绍一个方法让你的搜索更精准。

具体方法就是多个关键字同时搜索，嘻嘻，就是这么简单。想想也是，根据单个关键词查找出来的结果很可能比较多，关键词越多搜索结果就越少，代码定位也就越准确。

但是常规的搜索无法做到同时搜索多个关键词，因此需要借助搜索功能的**正则模式**。开启正则模式的开关就是下面示例截图中最右侧的 `.*` 符号，高亮状态就表示处于开启状态。

## 具体示例

例如现在有一个需求：将页面上的「产品名称」改为 「产品类型」

![](https://static.webjam.cn/images/202405/modify-shot.webp)

如果直接全局搜索「产品名称」会出现非常多的结果，结果显示在 10 个文件中出现了 12 次

![](https://static.webjam.cn/images/202405/vscode-search-01.webp)

再增加一个关键字「产品别名」，这样会搜索同时出现这两个关键字的代码文件，此时结果变成了在 8 个文件中出现了 8 次。

![](https://static.webjam.cn/images/202405/vscode-search-02.webp)

好吧，看起来并没有多大效果，这主要是刚好这两个关键字在业务中的关联性比较强，总是一起出现 😄，不要灰心，此时可以继续增加关键字。

![](https://static.webjam.cn/images/202405/vscode-search-03.webp)

当关键字增加到三个的时候，搜索结果只有三个了，此时很轻松就能判断哪个是我需要的了

## 一些提示

### （1） 关于正则

1、首先就是**一定要开启正则模式**，这是这个方法的前提条件。

2、虽然是正则模式，但是这里的输入框中**不需要正则表达式开头和结尾的斜线**，比如正常代码里正则表达式是这样的的 `/产品名称[\w\W]*?产品别名/`，而在 VSCode 搜索输入框是 `产品名称[\w\W]*?产品别名`。

3、不要担心正则写起来麻烦，因为咱们的使用场景已经固定，所以正则表达式的结构也是固定的，你只需要在关键词之间使用 `[\w\W]*?` 分隔开即可，多余的不用想（除非你有更多的需求或者对正则很熟悉）

例如上面例子中使用的正则就是这样的：

```
产品名称[\w\W]*?产品别名

资源ID[\w\W]*?产品名称[\w\W]*?产品别名
```

### （2） 关键词

关键字的选取很重要，选好关键字事半功倍，这和使用百度谷歌搜索资料是一样的道理。

1、尽量选择看起来应该属于同一个文件(模块)的，这是考虑到现在有经验的开发者都会进行模块化开发，如果你选择不相干的关键字可能他们并不存在于同一个文件内，这样反而适得其反。

2、还有就是选取的关键字最好是中文、英文、数字这三种类型，因为有一些符号在正则里面有特殊含义，遇到这些符号需要进行转义，反而更费时间，还记得咱们的目标吗？快速定位代码位置！

### （3） 辅助判断

稍有经验的开发者都会按照一定的规范和语义来组织代码文件结构，因此可以根据搜索结果中的文件路径进行辅助判断

## 理解上面的正则表达式

其实上面的正则表达式结构的适用范围挺广的

```
关键词A[\w\W]*?关键词B
```

- 其中 `[\w\W]` 匹配所有字符，包括单词字符（`\w`）和非单词字符（`\W`）；
- `*` 表示匹配一个或者多个，用于限制次数；
- 跟在 `*` 后面的 `?` 表示「非贪婪匹配模式」，这样的话关键词组合出现几次就匹配几次，如果没有 `?` 则最多匹配到一次。

下面也列出了一些能满足更特殊格式的正则

#### 两个关键字在同一行

```
关键词A(.*?)关键词B
```

#### 两个关键字在相邻的两行

```
关键词A(.*?\n.*?)关键词B
```

## 一个奇怪的 bug

按道理在正则中 `[\w\W]` 和 `[\s\S]` 的效果应该是一样的，但是我替换成 `[\s\S]` 之后却搜索失败，没有搜到任何结果。

```
产品名称[\s\S]+?产品别名
```

直接使用以下 js 代码在 Chrome Console 中进行验证：

```js
const str = `searchTypeMap: {
        'orderNumber': '订单号',
        'resourceId': '资源ID',
        'productName': '产品名称',
        'productAliasName': '产品别名',
      },`;

const regex = /产品名称[\s\S]+?产品别名/;
const match = str.match(regex);

if (match) {
  console.log(match[0]);
} else {
  console.log('没有匹配到相关字符串');
}
```

根据打印结果来看，这和我的预期是一样的，因此只能说 VSCode 中对 `[\w\W]` 和 `[\s\S]` 的处理有些细节地方不一样，使用 `[\w\W]` 可能更可靠。

所以在 VSCode 中我们暂时使用 `关键词A[\w\W]*?关键词B` 的方式进行正则搜索。

### 总结

在 VSCode 中搜索代码时**启用正则搜索**，可以同时搜索多个关键字，这样搜索结果更加精准。

多个关键字的格式为：`关键词A[\w\W]*?关键词B`，也就是说用 `[\w\W]*?` 将多个关键字隔开即可。
