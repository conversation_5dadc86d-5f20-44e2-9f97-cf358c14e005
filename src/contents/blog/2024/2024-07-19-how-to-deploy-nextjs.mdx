---
title: Next.js 项目如何部署？
slug: how-to-deploy-nextjs
summary: 详细介绍 Next.js 项目的多种部署方式，包括 Vercel 平台部署和自托管服务器部署方案
date: '2024-07-19'
featured: true
tags:
  - Nextjs
  - 部署
  - 静态导出
draft: true
featured_image_url:
---

# Next.js 项目部署指南

Next.js 作为一个灵活的 React 框架，提供了多种部署选项，可以根据项目需求和基础设施选择最合适的部署方式。本文将详细介绍 Next.js 项目的各种部署方案。

## 目录

- [部署到 Vercel（推荐）](#部署到-vercel推荐)
- [部署到自托管服务器](#部署到自托管服务器)
  - [Node.js 服务器部署](#nodejs-服务器部署)
  - [Docker 容器部署](#docker-容器部署)
  - [静态导出部署](#静态导出部署)
- [静态导出配置详解](#静态导出配置详解)
- [Nginx 配置示例](#nginx-配置示例)
- [常见问题与解决方案](#常见问题与解决方案)
- [参考资料](#参考资料)

## 部署到 Vercel（推荐）

Vercel 是 Next.js 的创建者开发的云平台，为 Next.js 应用提供了最佳的部署体验：

1. 创建 [Vercel 账户](https://vercel.com/signup)
2. 导入你的 Git 仓库
3. Vercel 会自动检测 Next.js 项目并使用最优配置
4. 应用部署完成后，会自动分配一个域名

Vercel 平台优势：
- 零配置部署
- 自动 HTTPS
- 全球 CDN
- 自动预览部署（每个 PR 创建独立环境）
- 性能分析和监控
- 无需额外配置即可支持所有 Next.js 功能

## 部署到自托管服务器

### Node.js 服务器部署

适用于需要 Next.js 所有功能（如 API 路由、服务端渲染等）的项目：

1. 构建生产版本：
```bash
npm run build
# 或
yarn build
# 或
pnpm build
```

2. 启动 Node.js 服务器：
```bash
npm run start
# 或
yarn start
# 或
pnpm start
```

默认情况下，Next.js 服务器会在端口 3000 上运行。如果需要指定其他端口号，有以下几种方法：

**方法一：使用环境变量**

```bash
# Linux/macOS
PORT=4000 npm run start

# Windows (CMD)
set PORT=4000 && npm run start

# Windows (PowerShell)
$env:PORT=4000; npm run start
```

**方法二：修改 package.json**

```json
"scripts": {
  "dev": "next dev",
  "build": "next build",
  "start": "next start -p 4000",
  "lint": "next lint"
}
```

**方法三：使用 cross-env 包（跨平台解决方案）**

首先安装 cross-env：
```bash
npm install --save-dev cross-env
```

然后在 package.json 中配置：
```json
"scripts": {
  "dev": "next dev",
  "build": "next build",
  "start": "cross-env PORT=4000 next start",
  "start:prod": "cross-env NODE_ENV=production PORT=8080 next start",
  "lint": "next lint"
}
```

这样可以更灵活地在不同环境中指定不同的端口号。

3. 使用 PM2 等进程管理工具保持服务运行：
```bash
# 安装 PM2
npm install -g pm2

# 基本启动
pm2 start npm --name "next-app" -- start

# 指定端口启动
pm2 start npm --name "next-app" -- start -- -p 4000

# 使用环境变量指定端口
PORT=4000 pm2 start npm --name "next-app" -- start

# 使用配置文件启动
# 创建 ecosystem.config.js 文件
```

PM2 配置文件示例 (`ecosystem.config.js`):

```javascript
module.exports = {
  apps: [
    {
      name: "next-app",
      script: "npm",
      args: "start",
      env: {
        NODE_ENV: "production",
        PORT: 4000,
      },
      instances: "max", // 或指定数量，如 2
      exec_mode: "cluster", // 使用集群模式提高性能
      watch: false,
      max_memory_restart: "1G", // 内存超过 1G 自动重启
      log_date_format: "YYYY-MM-DD HH:mm:ss",
    },
		{
      name: "next-app",
			// 第二种方式是直接运行 npx next start 命令
			script: "npx",
      args: "next start",
      env: {
        NODE_ENV: "production",
        PORT: 4000,
      },
		}
  ],
};
```

然后使用配置文件启动:
```bash
pm2 start ecosystem.config.js
```

PM2 常用命令:
```bash
# 查看所有应用状态
pm2 list

# 查看特定应用详情
pm2 show next-app

# 查看日志
pm2 logs next-app

# 重启应用
pm2 restart next-app

# 停止应用
pm2 stop next-app

# 删除应用
pm2 delete next-app
```

### Docker 容器部署

创建 `Dockerfile`：

```dockerfile
FROM node:18-alpine AS base

# 安装依赖
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json* ./
RUN npm ci

# 构建应用
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

# 生产环境
FROM base AS runner
WORKDIR /app
ENV NODE_ENV production

COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000
# 设置默认端口为 3000，可以在运行容器时通过环境变量覆盖
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

在 `next.config.js` 中启用独立输出：

```js
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone'
}

module.exports = nextConfig
```

运行 Docker 容器时指定端口：

```bash
# 构建 Docker 镜像
docker build -t nextjs-app .

# 运行容器（默认端口 3000）
docker run -p 3000:3000 nextjs-app

# 运行容器并指定自定义端口
docker run -p 8080:3000 nextjs-app

# 通过环境变量覆盖容器内部端口
docker run -p 8080:8080 -e PORT=8080 nextjs-app

# 使用 docker-compose
# docker-compose.yml 示例:
# version: '3'
# services:
#   nextjs:
#     build: .
#     ports:
#       - "8080:3000"
#     environment:
#       - NODE_ENV=production
#     restart: always
```

### 静态导出部署

对于不需要服务器端功能的项目，可以使用静态导出，部署到任何静态网站托管服务：

## 静态导出配置详解

在 Next.js 13.3.0 及以上版本中，静态导出的配置方式已更新。不再需要在 `package.json` 中添加 `export` 命令，而是通过 `next.config.js` 配置：

```js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用静态导出
  output: 'export',

  // 可选：将链接 `/me` 转换为 `/me/` 并生成 `/me/index.html` 而非 `/me.html`
  // trailingSlash: true,

  // 可选：更改输出目录，默认为 `out`
  // distDir: 'dist',

  // 如果使用 next/image，需要配置自定义加载器
  images: {
    unoptimized: true,
    // 或使用自定义加载器
    // loader: 'custom',
    // loaderFile: './my-loader.ts',
  }
}

module.exports = nextConfig
```

执行构建命令即可生成静态文件：

```bash
npm run build
# 或
yarn build
# 或
pnpm build
```

构建完成后，静态文件将生成在 `out` 目录中（除非你自定义了 `distDir`）。

## 静态导出的功能支持

静态导出支持的功能：
- 服务器组件（在构建时运行）
- 客户端组件
- 动态路由（使用 `generateStaticParams()`）
- 使用 `next/link` 的预取
- JavaScript 预加载
- 动态导入
- 各种样式选项（CSS Modules、styled-jsx 等）
- 客户端数据获取
- 图像优化（使用自定义加载器）
- 路由处理程序（仅支持 GET 方法）

不支持的功能：
- 没有 `generateStaticParams()` 的动态路由
- 依赖请求的路由处理程序
- Cookies
- 重写（Rewrites）
- 重定向（Redirects）
- 头信息（Headers）
- 中间件（Middleware）
- 增量静态再生（ISR）
- 草稿模式（Draft Mode）
- 服务器操作（Server Actions）
- 拦截路由（Intercepting Routes）

## Nginx 配置示例

将静态导出的 Next.js 应用部署到 Nginx 服务器上，需要正确配置 `nginx.conf`。以下是一个基本配置示例，包括端口设置：

```nginx
# 基本配置 - 监听 80 端口
server {
  listen 80;
  server_name yourdomain.com;

  root /var/www/out;  # 静态文件目录

  location / {
    try_files $uri $uri.html $uri/ =404;
  }

  # 当 trailingSlash: false 时需要此配置
  # 对于动态路由如 /blog/[id] 特别重要
  location /blog/ {
    rewrite ^/blog/(.*)$ /blog/$1.html break;
  }

  # 错误页面配置
  error_page 404 /404.html;
  location = /404.html {
    internal;
  }
}

# HTTPS 配置 - 监听 443 端口
server {
  listen 443 ssl http2;
  server_name yourdomain.com;

  # SSL 证书配置
  ssl_certificate /etc/nginx/ssl/yourdomain.com.crt;
  ssl_certificate_key /etc/nginx/ssl/yourdomain.com.key;
  ssl_protocols TLSv1.2 TLSv1.3;
  ssl_prefer_server_ciphers on;

  root /var/www/out;

  # 其他配置与 HTTP 相同
  location / {
    try_files $uri $uri.html $uri/ =404;
  }

  location /blog/ {
    rewrite ^/blog/(.*)$ /blog/$1.html break;
  }

  error_page 404 /404.html;
  location = /404.html {
    internal;
  }
}

# HTTP 重定向到 HTTPS（可选）
server {
  listen 80;
  server_name yourdomain.com;
  return 301 https://$host$request_uri;
}
```

## 常见问题与解决方案

### 1. 图片优化问题

使用静态导出时，默认的图像优化不可用。解决方案：

- 使用 `next/image` 的 `unoptimized` 属性
- 配置自定义图像加载器（如 Cloudinary、Imgix 等）

### 2. 环境变量处理

静态导出时，环境变量在构建时被注入，无法在运行时更改。解决方案：

- 使用 `NEXT_PUBLIC_` 前缀的环境变量
- 实现运行时配置（如 `window.__ENV__`）

### 3. API 路由替代方案

静态导出不支持 API 路由，替代方案：

- 使用无服务器函数（Serverless Functions）
- 使用独立的 API 服务
- 使用客户端直接调用外部 API

### 4. 自定义端口问题

在不同环境中可能需要使用不同的端口：

- **开发环境**：使用 `next dev -p 端口号` 指定开发服务器端口
- **生产环境**：使用上文提到的方法指定生产服务器端口
- **静态导出**：静态文件本身不包含端口信息，由托管服务器（如 Nginx）决定

### 5. 多端口部署

有时需要在同一服务器上部署多个 Next.js 应用：

```bash
# 应用 1
PORT=3000 pm2 start npm --name "app1" -- start

# 应用 2
PORT=3001 pm2 start npm --name "app2" -- start
```

Nginx 反向代理配置示例：

```nginx
# 应用 1
server {
  listen 80;
  server_name app1.yourdomain.com;

  location / {
    proxy_pass http://localhost:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
  }
}

# 应用 2
server {
  listen 80;
  server_name app2.yourdomain.com;

  location / {
    proxy_pass http://localhost:3001;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
  }
}
```

## 参考资料

- [Next.js 官方部署文档](https://nextjs.org/docs/app/building-your-application/deploying)
- [Next.js 静态导出指南](https://nextjs.org/docs/app/guides/static-exports)
- [Vercel 部署指南](https://vercel.com/docs/frameworks/nextjs)
- [PM2 官方文档](https://pm2.keymetrics.io/docs/usage/quick-start/)
- [Nginx 配置指南](https://nginx.org/en/docs/)
- [Docker 与 Next.js](https://nextjs.org/docs/app/building-your-application/deploying#docker-image)
- [一次构建多处部署 - Next.js Runtime Env](https://nextjs-book.innei.in/reading/recipes/runtime-env-and-build-once-deploy-many)
