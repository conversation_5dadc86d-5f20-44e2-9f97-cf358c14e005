---
title: 我的博客
slug: hello-blog
summary: '全新的个人网站, awesome!'
date: '2024-01-10'
featured: true
tags:
  - blog
draft: true
featured_image_url: https://static.webjam.cn/images/202401/aulianza.webp
---

前不久在 [GitHub](https://github.com/) 上看到一个很惊艳我的个人网站：

![](https://static.webjam.cn/images/202401/aulianza.webp)

- 网站地址：[Ryan Aulia](https://aulianza.id/)
- 代码仓库：[aulianza/aulianza.id](https://github.com/aulianza/aulianza.id)

## 我也想要这样一个网站

我一直没有一个很满意的个人网站，主要原因是个人审美/设计能力不达标，做出来的网站总感觉差点意思。

直到我看到今天的主角：这不就是我的「梦中情网」吗？刚好技术栈我也感兴趣（基于 Next.js, TypeScript, Tailwind CSS 构建），一切都刚刚好。

于是乎，我基于 [aulianza/aulianza.id](https://github.com/aulianza/aulianza.id)(GPL-3.0 license) 修改了上百个文件诞生了这个项目：[wencaizhang/webjam.cn · GitHub](https://github.com/wencaizhang/webjam.cn)

项目使用 Vercel 部署：[webjam.cn](https://webjam.cn)

## 为什么要修改这么多文件？

答案很简单：不修改没法儿用啊。

原仓库的一些特点：

- 博客内容和评论都是通过 API 调用 [dev.to](https://dev.to/) 的数据
- 实时在线留言，使用 [Firebase](https://firebase.google.com/?hl=zh-cn) 数据库
- 支持 [ChatGPT AI](https://openai.com/) 对话
- 显示 [Spotify](https://open.spotify.com/?) 实时信息
- 通过 [Cal.com](https://cal.com/) 预约会议
- 使用 [Web3Forms](https://web3forms.com/) 填写联系表单
- 甚至一些本地数据（本地文件信息）都是以 API 接口形式调用
- 个人信息/站点信息**硬编码**到项目中

所以这个项目没办法直接使用的，我修改了上百个文件，现在就好多了：

- 博客文章是本地文件，评论可使用 giscus（可选择不用）
- 关闭在线留言功能(Guestbook)功能
- ChatGPT AI、Spotify、Cal.com、Web3Forms、Github 分析、wakatime 记录都改为可配置项，自由决定是否启用
- 「Project」和 「learn」功能取消 API 层，直接在 `getStaticProps` 中完成数据获取
- 所有个人信息/站点信息改为可配置项
- 更容易扩展和定制（例如 menu 和技术栈）
- 增加「周刊」功能
- 一些优化：例如减少 `getStaticProps` 数据体积、链接代替 js 跳转等等
- 更多小细节...

## Roadmap

不过现在还是不够完美的，也就是能用，下一步我的计划是：

- [ ] 提供使用文档
- [ ] 增加 RSS 功能
- [ ] 增加自动化脚本，创建博客模版功能，这样就不需要手动复制粘贴 frontMattter
- [ ] 替换 markdown 渲染库，支持 mdx 渲染，提供更多组件增强 mdx 表现力
- [ ] 优化 「learn」 改成文档布局
- [ ] 扩展 「dashboard」 信息
- [ ] 优化 「weekly」 页面
- [x] 增加 「代码段」模块
- [ ] 重构代码结构（ contents 放在最外层，nextjs 代码作为主题放在某个子目录下）

## 如何运行

克隆代码

```sh
git clone https://github.com/webjam.cn
```

安装依赖

```sh
cd webjam.cn && pnpm install

# 或者
cd webjam.cn && yarn
```

启动开发服务

```sh
pnpm run dev
# 或者
yarn dev
```

然后修改 `src/contents/` 里面的内容即可。

## 如果你有一些想法或者建议

如果你有什么想法或者建议，可以通过 [Issues](https://github.com/wencaizhang/webjam.cn/issues/new) 告诉我或者直接给我[发邮件](mailto:<EMAIL>)。

当然如果你有能力对代码进行优化也欢迎直接 PR。
