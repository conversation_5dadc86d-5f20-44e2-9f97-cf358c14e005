---
title: AutoAnimate：一行代码为你的应用添加流畅动画
slug: AutoAnimate
summary: 探索AutoAnimate这个零配置、即插即用的动画工具，它能为你的Web应用添加流畅的过渡效果，支持React、Vue、Solid等多种框架。
date: '2024-07-15'
featured: true
tags: ['JavaScript', '前端', '动画', 'React', 'Vue']
draft: false
featured_image_url: https://cdn.formk.it/web-assets/logo-auto-animate.svg
---

# AutoAnimate：一行代码为你的应用添加流畅动画

在前端开发中，动画效果对于提升用户体验至关重要。然而，实现流畅、自然的动画往往需要编写大量代码，或者依赖复杂的动画库。今天我要介绍的 [AutoAnimate](https://auto-animate.formkit.com/) 是一个由FormKit团队开发的零配置、即插即用的动画工具，它能够以最小的代码量为你的Web应用添加流畅的过渡效果。

## 什么是 AutoAnimate?

AutoAnimate是一个轻量级的JavaScript动画工具，它的核心理念是"用一行代码添加动画"。它不需要复杂的配置，也不需要修改现有代码结构，只需简单地将其应用到父元素上，就能自动为其子元素添加平滑的过渡动画。

AutoAnimate支持多种前端框架，包括：
- React
- Vue
- Solid
- Svelte
- Angular
- 原生JavaScript

## 为什么选择AutoAnimate？

与其他动画库相比，AutoAnimate有以下优势：

1. **零配置**：无需复杂设置，一行代码即可使用
2. **轻量级**：压缩后仅约3KB，对性能影响极小
3. **框架无关**：支持多种流行的前端框架
4. **自动化**：自动检测DOM变化并应用适当的动画
5. **可定制**：提供插件系统用于自定义动画效果

## AutoAnimate能做什么？

AutoAnimate主要针对以下三种DOM变化自动添加动画效果：

1. 子元素被**添加**到DOM中
2. 子元素从DOM中**移除**
3. 子元素在DOM中**移动**位置

这使得它特别适合以下场景：
- 列表项的添加、删除和排序
- 折叠面板（Accordion）的展开和收起
- 卡片元素的添加和移除
- 表单验证消息的显示和隐藏
- 任何涉及DOM元素添加、移除或重排的场景

## 安装与基本使用

### 安装

使用你喜欢的包管理器安装：

```bash
# npm
npm install @formkit/auto-animate

# yarn
yarn add @formkit/auto-animate

# pnpm
pnpm add @formkit/auto-animate
```

### 基本使用

AutoAnimate的核心是一个名为`autoAnimate`的函数，它接受一个父元素作为参数。下面是在不同框架中的基本用法：

#### 原生JavaScript

```html
<div id="dropdown" class="dropdown">
  <strong class="dropdown-label" onclick="toggle">
    Click me to open!
  </strong>
</div>

<script type="module">
  import autoAnimate from './js/autoAnimate.js'
  const dropdown = document.getElementById('dropdown')
  autoAnimate(dropdown)

  const p = document.createElement('p')
  p.innerText = "Lorum ipsum..."

  function toggle () {
    dropdown.contains(p) ? p.remove() : dropdown.appendChild(p)
  }
</script>
```

#### React

React用户可以使用`useAutoAnimate` hook：

```jsx
import { useState, useRef, useEffect } from 'react'
import autoAnimate from '@formkit/auto-animate'

const Dropdown = () => {
  const [show, setShow] = useState(false)
  const parent = useRef(null)

  useEffect(() => {
    parent.current && autoAnimate(parent.current)
  }, [parent])

  const reveal = () => setShow(!show)

  return <div ref={parent}>
    <strong className="dropdown-label" onClick={reveal}>Click me to open!</strong>
    { show && <p className="dropdown-content" >Lorum ipsum...</p> }
  </div>
}

export default Dropdown
```

#### Vue

Vue用户可以使用`v-auto-animate`指令或`useAutoAnimate`组合式API：

```vue
<script setup>
import { ref, onMounted } from "vue"
import autoAnimate from "@formkit/auto-animate"

const dropdown = ref() // we need a DOM node
const show = ref(false)

onMounted(() => {
  autoAnimate(dropdown.value) // thats it!
})
</script>

<template>
  <div ref="dropdown" class="dropdown">
    <strong class="dropdown-label" @click="show = !show">
      Click me to open!
    </strong>
    <p class="dropdown-content" v-if="show">Lorum ipsum...</p>
  </div>
</template>
```

## 配置选项

虽然AutoAnimate的设计理念是零配置，但它也提供了一些配置选项来满足特定需求：

```javascript
autoAnimate(parent, {
  // 动画持续时间（毫秒）
  duration: 250,
  // 动画缓动函数
  easing: 'ease-in-out',
  // 是否启用动画
  disrespectUserMotionPreference: false
})
```

## 自定义动画效果

如果默认的动画效果不能满足你的需求，AutoAnimate还提供了插件系统来自定义动画效果。你可以通过提供一个函数作为`autoAnimate`的第二个参数来创建自定义动画：

```javascript
import { autoAnimate } from '@formkit/auto-animate'

// 创建"弹跳"效果的自定义动画
const bouncy = (el, keyframes, options) => {
  // 根据动作类型返回不同的关键帧
  return new KeyframeEffect(
    el,
    [
      { transform: 'scale(0.9)', opacity: 0 },
      { transform: 'scale(1.1)', opacity: 1, offset: 0.5 },
      { transform: 'scale(1)', opacity: 1 }
    ],
    {
      duration: options.duration,
      easing: 'ease-in-out'
    }
  )
}

// 应用自定义动画
autoAnimate(parent, bouncy)
```

## 实际应用示例

### 列表排序

```jsx
function SortableList() {
  const [parent] = useAutoAnimate()
  const [items, setItems] = useState(['React', 'Vue', 'Angular', 'Svelte'])

  const sortAZ = () => {
    setItems([...items].sort())
  }

  const sortZA = () => {
    setItems([...items].sort().reverse())
  }

  return (
    <div>
      <button onClick={sortAZ}>Sort A-Z</button>
      <button onClick={sortZA}>Sort Z-A</button>
      <ul ref={parent}>
        {items.map(item => (
          <li key={item}>{item}</li>
        ))}
      </ul>
    </div>
  )
}
```

### 折叠面板

```jsx
function Accordion() {
  const [parent] = useAutoAnimate()
  const [openItem, setOpenItem] = useState(null)

  const toggle = (id) => {
    setOpenItem(openItem === id ? null : id)
  }

  return (
    <div ref={parent}>
      {faqItems.map(item => (
        <div key={item.id}>
          <h3 onClick={() => toggle(item.id)}>{item.question}</h3>
          {openItem === item.id && <p>{item.answer}</p>}
        </div>
      ))}
    </div>
  )
}
```

## 性能考虑

AutoAnimate使用Web Animations API来实现动画效果，这比使用CSS过渡或JavaScript动画库更高效。它还会自动尊重用户的`prefers-reduced-motion`设置，除非你显式禁用这一行为。

对于大型列表或频繁更新的元素，可以考虑以下优化措施：

1. 在不需要动画时禁用AutoAnimate
2. 为动画设置较短的持续时间
3. 避免在一次更新中添加/移除大量元素

## 结论

AutoAnimate是一个简单而强大的工具，它能够以最小的代码量为你的Web应用添加流畅的动画效果。无论你使用哪种前端框架，AutoAnimate都能帮助你提升用户体验，让你的应用更加生动和专业。

如果你正在寻找一种简单的方法来为你的应用添加动画，而又不想深入学习复杂的动画库，那么AutoAnimate绝对值得一试。正如其口号所说："用一行代码添加动画"，它确实做到了这一点。

## 相关链接

- [AutoAnimate官方网站](https://auto-animate.formkit.com/)
- [GitHub仓库](https://github.com/formkit/auto-animate)
- [FormKit官方网站](https://formkit.com/)
