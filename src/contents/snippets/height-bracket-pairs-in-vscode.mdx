---
title: 如何在 VSCode 中高亮括号对参考线
description: 启用括号对参考线能帮助我们更方便阅读代码
slug: height-bracket-pairs-in-vscode
date: '2024-01-22'
featured: fasle
type: vscode
draft: true
---

打开 VSCode 设置，然后搜索 `bracket pairs` 关键字，就会出现这两个设置项：

![VSCode bracket pairs 配置](https://static.webjam.cn/images/202401/vscode-bracket-pairs.webp)

共 3 个选项：

- `true`: 高亮所有的括号对参考线
- `active`: 仅高亮鼠标所在位置的括号对参考线
- `false`: 禁用所有的括号对参考线

选择 `true` 和 `active` 都可以，具体效果对比如下：

![VSCode 启用括号对辅助线对比](https://static.webjam.cn/images/202401/vscode-bracket-pairs-values.webp)

我选择的是 `active`，这样界面看起来更加简洁一些。
