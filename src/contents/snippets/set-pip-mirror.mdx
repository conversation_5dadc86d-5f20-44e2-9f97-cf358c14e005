---
title: 如何设置pip镜像源加速Python包安装
description: 详细指南：修改pip镜像源为国内源以加速Python包下载
slug: set-pip-mirror
date: '2025-05-22'
featured: true
type: python
draft: false
---

## TLDR

临时使用镜像

```bash
# 临时使用
pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple <some-package>
```

例如，临时使用本镜像站来升级 pip：

```bash
python -m pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple --upgrade pip
```

设为全局默认镜像源

```bash
pip config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple
```

## 为什么要设置pip镜像源？

默认的pip源服务器位于国外，国内用户下载Python包时可能会遇到：
- 下载速度慢
- 连接不稳定
- 频繁超时

通过修改为国内镜像源，可以显著提高包下载速度和稳定性。

## 国内常用pip镜像源

推荐使用以下国内镜像源：

1. **清华大学**：`https://pypi.tuna.tsinghua.edu.cn/simple` (文档：[pypi | 镜像站使用帮助 | 清华大学开源软件镜像站 | Tsinghua Open Source Mirror](https://mirrors.tuna.tsinghua.edu.cn/help/pypi/))
2. **阿里云**：`https://mirrors.aliyun.com/pypi/simple`
3. **豆瓣**：`https://pypi.douban.com/simple`
4. **华为云**：`https://repo.huaweicloud.com/repository/pypi/simple`

## 临时使用镜像源

在pip安装命令后添加`-i`参数：
```bash
pip install package-name -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 永久设置镜像源

### 方法一：修改pip配置文件

1. 创建或编辑pip配置文件：
   ```bash
   mkdir -p ~/.pip
   nano ~/.pip/pip.conf
   ```

2. 添加以下内容（以清华大学源为例）：
   ```ini
   [global]
   index-url = https://pypi.tuna.tsinghua.edu.cn/simple
   trusted-host = pypi.tuna.tsinghua.edu.cn
   ```

### 方法二：使用命令行设置

```bash
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn
```

## 验证镜像源是否生效

1. 查看当前配置：
   ```bash
   pip config list
   ```

2. 测试下载速度：
   ```bash
   pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple
   ```

## 注意事项

1. 使用HTTPS源更安全
2. 某些企业网络可能需要配置代理
3. 如果遇到SSL错误，可以添加`--trusted-host`参数：
   ```bash
   pip install package-name --trusted-host pypi.tuna.tsinghua.edu.cn -i https://pypi.tuna.tsinghua.edu.cn/simple
   ```

## 常见问题

**Q: 如何恢复默认源？**
A: 删除pip配置文件或执行：
```bash
pip config unset global.index-url
```

**Q: 为什么设置了镜像源还是下载慢？**
A: 可能是：
1. 镜像源本身有问题，尝试换其他源
2. 网络连接问题，检查网络设置

**Q: 如何为特定项目设置不同的源？**
A: 在项目目录下创建`pip.ini`文件，内容格式与全局配置相同

**Q: Windows系统如何设置？**
A: 配置文件路径为：`%APPDATA%\pip\pip.ini`，其他操作与Linux相同
