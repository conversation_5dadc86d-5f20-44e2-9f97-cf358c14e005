// 性能监控工具
interface PerformanceMetric {
  operation: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private completedMetrics: PerformanceMetric[] = [];

  // 开始监控一个操作
  start(operationId: string, operation: string, metadata?: Record<string, any>): void {
    this.metrics.set(operationId, {
      operation,
      startTime: typeof performance !== 'undefined' ? performance.now() : Date.now(),
      metadata,
    });
  }

  // 结束监控一个操作
  end(operationId: string): number | null {
    const metric = this.metrics.get(operationId);
    if (!metric) {
      console.warn(`Performance metric not found: ${operationId}`);
      return null;
    }

    const endTime = typeof performance !== 'undefined' ? performance.now() : Date.now();
    const duration = endTime - metric.startTime;

    const completedMetric: PerformanceMetric = {
      ...metric,
      endTime,
      duration,
    };

    this.completedMetrics.push(completedMetric);
    this.metrics.delete(operationId);

    console.log(`⏱️ ${metric.operation}: ${duration.toFixed(2)}ms`);
    return duration;
  }

  // 获取所有完成的指标
  getMetrics(): PerformanceMetric[] {
    return [...this.completedMetrics];
  }

  // 获取特定操作的统计信息
  getOperationStats(operation: string) {
    const operationMetrics = this.completedMetrics.filter(m => m.operation === operation);
    if (operationMetrics.length === 0) {
      return null;
    }

    const durations = operationMetrics.map(m => m.duration!);
    return {
      count: operationMetrics.length,
      total: durations.reduce((sum, d) => sum + d, 0),
      average: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      min: Math.min(...durations),
      max: Math.max(...durations),
    };
  }

  // 清理指标
  clear(): void {
    this.metrics.clear();
    this.completedMetrics = [];
  }

  // 生成性能报告
  generateReport(): string {
    const operations = [...new Set(this.completedMetrics.map(m => m.operation))];
    let report = '\n📊 Performance Report\n';
    report += '='.repeat(50) + '\n';

    operations.forEach(operation => {
      const stats = this.getOperationStats(operation);
      if (stats) {
        report += `\n${operation}:\n`;
        report += `  Count: ${stats.count}\n`;
        report += `  Total: ${stats.total.toFixed(2)}ms\n`;
        report += `  Average: ${stats.average.toFixed(2)}ms\n`;
        report += `  Min: ${stats.min.toFixed(2)}ms\n`;
        report += `  Max: ${stats.max.toFixed(2)}ms\n`;
      }
    });

    return report;
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 装饰器函数，用于自动监控函数执行时间
export function measurePerformance(operationName: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const operationId = `${operationName}-${Date.now()}-${Math.random()}`;
      performanceMonitor.start(operationId, operationName, {
        function: propertyKey,
        args: args.length,
      });

      try {
        const result = await originalMethod.apply(this, args);
        performanceMonitor.end(operationId);
        return result;
      } catch (error) {
        performanceMonitor.end(operationId);
        throw error;
      }
    };

    return descriptor;
  };
}

// 简单的性能测量函数
export async function measureAsync<T>(
  operation: string,
  fn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const operationId = `${operation}-${Date.now()}-${Math.random()}`;
  performanceMonitor.start(operationId, operation, metadata);

  try {
    const result = await fn();
    performanceMonitor.end(operationId);
    return result;
  } catch (error) {
    performanceMonitor.end(operationId);
    throw error;
  }
}

export function measureSync<T>(
  operation: string,
  fn: () => T,
  metadata?: Record<string, any>
): T {
  const operationId = `${operation}-${Date.now()}-${Math.random()}`;
  performanceMonitor.start(operationId, operation, metadata);

  try {
    const result = fn();
    performanceMonitor.end(operationId);
    return result;
  } catch (error) {
    performanceMonitor.end(operationId);
    throw error;
  }
}
