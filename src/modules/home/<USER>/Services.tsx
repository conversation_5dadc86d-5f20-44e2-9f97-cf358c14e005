'use client';

import Link from 'next/link';
import { BiRocket as RocketIcon } from 'react-icons/bi';

import Button from '@/common/components/elements/Button';
import Card from '@/common/components/elements/Card';
import SectionHeading from '@/common/components/elements/SectionHeading';
import { author } from '@/contents/siteMetadata';

const Services = () => {
  return (
    <section className='space-y-5'>
      <div className='space-y-3'>
        <SectionHeading title="What I've been working on" />
        <p className='leading-[1.8] md:leading-loose text-neutral-800 dark:text-neutral-300'>
          {author.workingOn}
        </p>
      </div>
      <Card className='p-8 bg-neutral-100 border dark:border-none rounded-xl space-y-4'>
        <div className='flex gap-2 items-center'>
          <RocketIcon size={24} />
          <h3 className='text-xl font-medium'>Lets work together!</h3>
        </div>
        <p className='leading-[1.8] md:leading-loose text-neutral-800 dark:text-neutral-300 pl-2'>
          {author.workTogether}
        </p>
        <Link href='/contact' className='inline-block'>
          <Button data-umami-event='Click Contact Button'>Contact me</Button>
        </Link>
      </Card>
    </section>
  );
};

export default Services;
