#!/usr/bin/env node

/**
 * 性能测试脚本
 * 用于测试 MDX 文件读取优化的效果
 */

const { performance } = require('perf_hooks');
const path = require('path');

// 模拟测试环境
process.env.NODE_ENV = 'development';

async function testPerformance() {
  console.log('🚀 开始性能测试...\n');

  try {
    // 动态导入 ES 模块
    const { getCollection, getEntry, clearCache, getCacheStats } = await import('../src/common/libs/mdx.ts');

    // 测试 1: 冷启动性能（无缓存）
    console.log('📊 测试 1: 冷启动性能');
    clearCache();
    
    const start1 = performance.now();
    const blogPosts = getCollection('blog', false);
    const end1 = performance.now();
    
    console.log(`   Blog 列表加载 (无内容): ${(end1 - start1).toFixed(2)}ms`);
    console.log(`   文章数量: ${blogPosts.length}`);

    // 测试 2: 缓存命中性能
    console.log('\n📊 测试 2: 缓存命中性能');
    
    const start2 = performance.now();
    const blogPostsCached = getCollection('blog', false);
    const end2 = performance.now();
    
    console.log(`   Blog 列表加载 (缓存): ${(end2 - start2).toFixed(2)}ms`);
    console.log(`   性能提升: ${((end1 - start1) / (end2 - start2)).toFixed(1)}x`);

    // 测试 3: 内容加载性能对比
    console.log('\n📊 测试 3: 内容加载性能对比');
    
    clearCache();
    const start3 = performance.now();
    const blogPostsWithContent = getCollection('blog', true);
    const end3 = performance.now();
    
    console.log(`   Blog 列表加载 (含内容): ${(end3 - start3).toFixed(2)}ms`);
    console.log(`   vs 无内容加载: ${((end3 - start3) / (end1 - start1)).toFixed(1)}x 时间`);

    // 测试 4: 单文章加载
    console.log('\n📊 测试 4: 单文章加载性能');
    
    if (blogPosts.length > 0) {
      const firstSlug = blogPosts[0].slug;
      
      const start4 = performance.now();
      const singlePost = getEntry('blog', firstSlug);
      const end4 = performance.now();
      
      console.log(`   单文章加载: ${(end4 - start4).toFixed(2)}ms`);
      console.log(`   文章标题: ${singlePost?.frontMatter?.title || 'Unknown'}`);
    }

    // 测试 5: Cheatsheet 性能
    console.log('\n📊 测试 5: Cheatsheet 性能');
    
    clearCache();
    const start5 = performance.now();
    const snippets = getCollection('snippets', false);
    const end5 = performance.now();
    
    console.log(`   Snippets 列表加载: ${(end5 - start5).toFixed(2)}ms`);
    console.log(`   Snippets 数量: ${snippets.length}`);

    // 缓存统计
    console.log('\n📊 缓存统计');
    const cacheStats = getCacheStats();
    console.log(`   缓存条目数: ${cacheStats.cacheSize}`);
    console.log(`   文件统计缓存: ${cacheStats.fileStatsSize}`);
    console.log(`   缓存键: ${cacheStats.cacheEntries.join(', ')}`);

    // 内存使用情况
    console.log('\n📊 内存使用情况');
    const memUsage = process.memoryUsage();
    console.log(`   RSS: ${(memUsage.rss / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   Heap Used: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   Heap Total: ${(memUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`);

    console.log('\n✅ 性能测试完成!');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testPerformance().catch(console.error);
