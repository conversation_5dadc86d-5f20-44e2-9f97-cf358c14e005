<?xml version='1.0' encoding='utf-8'?>
<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 500 420" version="1.1" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:1.5;"> <rect id="zsh-console-macos-forced-colors-light" x="0" y="0" width="500" height="420" style="fill:none;"/> <clipPath id="_clip1"> <rect x="0" y="0" width="500" height="420"/> </clipPath> <g clip-path="url(#_clip1)"> <rect id="window-shadow" x="0" y="0" width="500" height="420" style="fill:url(#_Radial2);"/> <path id="window-decoration" d="M455,64.71C455,52.728 445.272,43 433.29,43L66.71,43C54.728,43 45,52.728 45,64.71L45,351.282C45,365.476 56.524,377 70.718,377L429.282,377C443.476,377 455,365.476 455,351.282L455,64.71Z" style="fill:none;"/> <path id="window" d="M455,67.048C455,53.776 444.224,43 430.952,43L69.048,43C55.776,43 45,53.776 45,67.048L45,346.94C45,363.531 58.469,377 75.06,377L424.94,377C441.531,377 455,363.531 455,346.94L455,67.048Z" style="fill:#fff;stroke:#000;stroke-width:2px;"/> <circle id="window-button-fullscreen" cx="146" cy="71" r="14" style="fill:#00ff26;stroke:#000;stroke-width:2px;"/> <circle id="window-button-minimize" cx="110" cy="71" r="14" style="fill:#ffae00;stroke:#000;stroke-width:2px;"/> <circle id="window-button-close" cx="74" cy="71" r="14" style="fill:#ff0c00;stroke:#000;stroke-width:2px;"/> <path id="console" d="M455,99L45,99L45,349.2C45,364.543 57.457,377 72.8,377L427.2,377C442.543,377 455,364.543 455,349.2L455,99Z"/> <rect id="console-decoration" x="45" y="99" width="410.024" height="1" style="fill:none;"/> <g id="console-text"> <path d="M101.977,156.888L74.591,192.908L102.265,192.908L101.162,200.534L63.416,200.534L63.416,193.675L90.994,157.655L65.622,157.655L65.622,149.981L101.977,149.981L101.977,156.888Z" style="fill:#fff;fill-rule:nonzero;"/> <path d="M138.477,194.395C142.154,194.395 145.056,193.723 147.182,192.38C149.309,191.037 150.372,189.215 150.372,186.913C150.372,185.346 150.076,184.051 149.484,183.028C148.893,182.004 147.734,181.069 146.007,180.222C144.28,179.374 141.674,178.503 138.189,177.608C134.416,176.649 131.315,175.593 128.884,174.442C126.454,173.291 124.568,171.788 123.225,169.934C121.882,168.079 121.21,165.729 121.21,162.883C121.21,160.038 122.018,157.552 123.633,155.425C125.247,153.299 127.534,151.66 130.491,150.509C133.449,149.358 136.894,148.782 140.827,148.782C148.181,148.782 154.417,150.685 159.533,154.49L155.6,160.485C153.457,159.046 151.219,157.943 148.885,157.176C146.551,156.408 143.929,156.025 141.019,156.025C137.438,156.025 134.816,156.568 133.153,157.655C131.49,158.743 130.659,160.261 130.659,162.212C130.659,163.619 131.051,164.77 131.834,165.665C132.618,166.56 133.921,167.376 135.743,168.111C137.566,168.847 140.268,169.71 143.849,170.701C147.494,171.692 150.484,172.796 152.818,174.011C155.152,175.226 156.975,176.824 158.285,178.807C159.596,180.789 160.252,183.315 160.252,186.385C160.252,189.838 159.245,192.716 157.23,195.018C155.216,197.32 152.562,199.015 149.269,200.102C145.975,201.189 142.378,201.733 138.477,201.733C134.16,201.733 130.323,201.102 126.966,199.838C123.609,198.575 120.715,196.889 118.285,194.778L123.369,188.927C125.415,190.622 127.725,191.957 130.299,192.932C132.873,193.907 135.599,194.395 138.477,194.395Z" style="fill:#fff;fill-rule:nonzero;"/> <path d="M187.543,156.552C189.525,154.026 191.915,152.1 194.713,150.773C197.511,149.446 200.429,148.782 203.466,148.782C208.23,148.782 211.788,150.077 214.138,152.667C216.488,155.257 217.663,158.95 217.663,163.747L217.663,200.534L208.454,200.534L208.454,164.562C208.454,161.556 207.807,159.366 206.512,157.991C205.217,156.616 203.162,155.929 200.349,155.929C197.855,155.929 195.48,156.712 193.226,158.279C190.972,159.846 189.077,161.748 187.543,163.986L187.543,200.534L178.334,200.534L178.334,129.741L187.543,128.734L187.543,156.552Z" style="fill:#fff;fill-rule:nonzero;"/> <path d="M230.661,199.239L273.875,132.571L279.535,135.928L236.177,202.596L230.661,199.239ZM242.364,133.242C245.242,133.242 247.736,133.922 249.846,135.281C251.956,136.64 253.563,138.486 254.666,140.821C255.769,143.155 256.321,145.729 256.321,148.543C256.321,151.356 255.769,153.938 254.666,156.289C253.563,158.639 251.956,160.509 249.846,161.9C247.736,163.291 245.242,163.986 242.364,163.986C239.486,163.986 236.984,163.291 234.858,161.9C232.731,160.509 231.101,158.639 229.966,156.289C228.83,153.938 228.263,151.356 228.263,148.543C228.263,145.729 228.822,143.155 229.942,140.821C231.061,138.486 232.691,136.64 234.834,135.281C236.976,133.922 239.486,133.242 242.364,133.242ZM242.364,139.286C240.445,139.286 238.919,140.069 237.783,141.636C236.648,143.203 236.081,145.505 236.081,148.543C236.081,151.516 236.648,153.842 237.783,155.521C238.919,157.2 240.445,158.039 242.364,158.039C244.282,158.039 245.793,157.248 246.896,155.665C247.999,154.082 248.551,151.708 248.551,148.543C248.551,145.377 248.007,143.043 246.92,141.54C245.833,140.037 244.314,139.286 242.364,139.286ZM268.839,170.366C271.717,170.366 274.211,171.053 276.321,172.428C278.432,173.803 280.038,175.649 281.142,177.968C282.245,180.286 282.796,182.868 282.796,185.714C282.796,188.527 282.237,191.101 281.118,193.435C279.998,195.77 278.392,197.632 276.297,199.023C274.203,200.414 271.717,201.11 268.839,201.11C265.961,201.11 263.459,200.414 261.333,199.023C259.207,197.632 257.576,195.77 256.441,193.435C255.306,191.101 254.738,188.527 254.738,185.714C254.738,182.9 255.298,180.326 256.417,177.992C257.536,175.657 259.167,173.803 261.309,172.428C263.451,171.053 265.961,170.366 268.839,170.366ZM268.839,176.409C266.921,176.409 265.394,177.184 264.259,178.735C263.124,180.286 262.556,182.612 262.556,185.714C262.556,188.655 263.124,190.965 264.259,192.644C265.394,194.323 266.921,195.162 268.839,195.162C270.758,195.162 272.268,194.371 273.372,192.788C274.475,191.205 275.026,188.847 275.026,185.714C275.026,182.516 274.483,180.166 273.396,178.663C272.308,177.16 270.79,176.409 268.839,176.409Z" style="fill:#fff;fill-rule:nonzero;"/> </g> </g> <defs> <radialGradient id="_Radial2" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(250,0,0,210,250,210)"><stop offset="0" style="stop-color:#000;stop-opacity:0.2"/></radialGradient> </defs> </svg>