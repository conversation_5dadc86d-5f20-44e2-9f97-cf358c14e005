## 基本信息

- 张文才 / 男 / 1993 年（32 岁）
- 前端开发工程师 / 郑州轻工业大学（本科/数学专业）
- [<EMAIL>](mailto:<EMAIL>) / 18037143856 （微信同号）

## 技术简介

- 熟练使用 `Vue` 技术栈（`vue2/3` / `vuex` / `pinia` / `vue-router` 等）
- 熟练使用 `React` 技术栈 (`Next.js` / `zustand` / `react-router` / `TanStack Query`等)
- 深入理解 `React/vue`, 并研究过其内部实现
- 熟练构建部署：`webpack` + `vite` + `docker` + `jenkins`
- 深入理解 `Webpack/vite`, 能够编写 Babel Loader, Plugin 进行工程化能力输出
- 熟练跨平台客户端: `Electron`
- 承担团队技术选型工作，执行能力强，注重前端标准化，在部门内部推行 Eslint Commitlint 以及标准落地
- 喜欢分享，工作认真负责注重效率，在公司获得过年度优秀员工

## 工作经历

- 2018.9 - 2024.11 北京友普信息技术有限公司 前端开发工程师
- 2016.8 - 2018.9 北京移动通信研究院 前端开发工程师

## 项目经验

### 友普云平台系统

基于 OpenStack 开发的适用于本公司产品和业务的**云计算管理平台**，友普云服务将原有云管理平台进行整合，实现了云服务管理、维护、运营、销售为一体的便捷平台，产品定位同华为云平台。

#### 技术栈

- `Vue2.6` + `Vuex` + `Ant Design Vue` + `Tailwind CSS` + `ECharts`

#### 负责内容

- 参与需求评审，负责实现前端需求的技术选型和难点攻克，
- 从零搭建项目，经历了整个项目的生命周期。
- 推动项目工程化和规范化
- 核心功能和公用组件开发
- 代码审核，进行 code review

#### 项目成果

- 实现 100+ 核心页面开发，封装公用组件 20+ 个, 前端工具方法 15+，提升开发效率
- 应用封装可视化 ECharts 折线图、柱状图等图标，并进行性能优化
- 大文件（系统镜像）上传下载性能优化（切片/重传/秒传/断点续传），提高上传成功率
- 引入 `@vue/composition-api` 提高代码可封装程度
- 封装权限控制（路由拦截/vue 指令等方式）
- `mixins` 封装公用功能（表格弹窗类）
- 封装油猴脚本解析(YApi 平台)接口参数自动生成 api 代码
- 基于 `axios` 封装链式拦截器，提高代码可读性
- 封装多侧边栏功能，根据当前路由展示相关菜单

### 手术麻醉系统

该系统主要为医院提供手术麻醉管理服务，旨在优化手术室排班、麻醉流程记录及风险控制。系统通过信息化手段，实现手术安排、麻醉记录实时录入与数据统计分析，提高医疗流程的规范性和工作效率，降低手术风险。

#### 技术栈：

- `Vue3` + `Ant Design Vue` + `pinia`

#### 工作内容

- 封装表单画布，包含拖拽添加组件、框选、拖动、缩放、对齐等功能
- 画布数据回显为正常表单
- 根据接口动态生成菜单和路由
- 根据业务需求改造第三方组件
- 封装 ECharts 图表可视化显示数据
- 完成核心功能开发

#### 项目成果

- 实现 30+ 核心页面开发
- 封装表单组件 30+
- 封装油猴脚本解析(Apipost 平台)接口参数自动生成 api 代码
- 支持各种尺寸 PDF 打印功能
- 优化画布性能，同一画布支持 1000+组件不卡顿

### 电厂集成优化系统（web + 客户端）

以设备模块为节点模型，通过拖动节点的方式组成系统，以连线的方式定义各设备的关系，构建仿真计算模型后，添加参数最终计算出指标参数的结果。

#### 技术栈

`React` + `Electron` + `zustand` + `react-flow` + `Shadcn/ui` + `Tailwind` + `daisyUI`

#### 主要内容

- 基于 `react-flow` 实现灵活的流程图编辑功能，支持节点的增删改查、拖拽连接及自定义属性配置，为后续计算模型的搭建提供直观操作界面。
- 根据业务封装各类自定义节点和连线
- 封装自定义节点外观功能
- 封装自定义节点数据功能
- 封装节点接受自定义代码功能
- 打包跨平台应用，同时支持 web 和客户端 APP
- 对数据使用 ECharts 进行图形化展示，如曲线图、柱状图等，并进行性能优化
- 数据 Execl 文件的导出和导入并展示到图表中
