{"name": "webjam.cn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 8283", "dev2": "next dev --turbo -p 8283", "build": "next build", "postbuild": "next-sitemap", "start": "next start -p 8283", "export": "next build", "static-build": "next build", "serve-static": "npx serve out", "lint": "next lint", "lint:fix": "eslint src --fix && pnpm format", "typecheck": "tsc --noEmit --incremental false", "format": "prettier --write .", "format:check": "prettier -c .", "test:watch": "jest --watch", "test": "jest", "test:ci": "jest --ci", "prepare": "husky install", "postinstall": "prisma generate", "new": "plop --plopfile plopfile.mjs", "new:blog": "plop --plopfile plopfile.mjs blog", "new:snippet": "plop --plopfile plopfile.mjs snippet", "new:learn": "plop --plopfile plopfile.mjs learn", "new:weekly": "plop --plopfile plopfile.mjs weekly", "new:project": "plop --plopfile plopfile.mjs project"}, "dependencies": {"@giscus/react": "^2.4.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.1.5", "@mdx-js/loader": "^2.3.0", "@mdx-js/react": "^2.3.0", "@monaco-editor/react": "^4.6.0", "@prisma/client": "^4.16.2", "aos": "^3.0.0-beta.6", "axios": "^1.6.2", "clsx": "^1.2.1", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "firebase": "^10.6.0", "framer-motion": "^10.16.16", "gray-matter": "^4.0.3", "json-to-ts": "^2.1.0", "moment": "^2.29.4", "next": "15.2.3", "next-auth": "^4.24.5", "next-seo": "^6.4.0", "next-sitemap": "^4.2.3", "next-themes": "^0.2.1", "nprogress": "^0.2.0", "prism-react-renderer": "^2.4.0", "prisma": "^4.16.2", "react": "18.3.1", "react-dom": "18.3.1", "react-icons": "^4.12.0", "react-infinite-scroll-component": "^6.1.0", "react-loading-skeleton": "^3.3.1", "react-markdown": "^8.0.7", "react-resizable-panels": "^0.0.55", "react-scroll": "^1.9.0", "react-slick": "^0.29.0", "react-spinners": "^0.13.8", "react-syntax-highlighter": "^15.5.0", "react-use-draggable-scroll": "^0.4.7", "remark": "^14.0.3", "remark-gfm": "^3.0.1", "remark-mdx": "^2.0.0", "remark-parse": "^10.0.0", "sharp": "^0.33.2", "slick-carousel": "^1.8.1", "swr": "^2.2.4", "tailwind-merge": "^1.14.0", "tailwind-scrollbar-hide": "^1.1.7", "typewriter-effect": "^2.21.0", "usehooks-ts": "^2.9.1", "uuid": "^9.0.1", "zustand": "^4.4.6"}, "devDependencies": {"@commitlint/cli": "^17.8.0", "@commitlint/config-conventional": "^17.8.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/aos": "^3.0.7", "@types/mdx": "^2.0.13", "@types/node": "20.1.7", "@types/nprogress": "^0.2.3", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@types/react-scroll": "^1.8.10", "@types/react-slick": "^0.23.13", "@types/react-syntax-highlighter": "^15.5.10", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^5.62.0", "autoprefixer": "10.4.19", "eslint": "8.57.0", "eslint-config-next": "15.2.3", "eslint-config-prettier": "^8.10.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^13.3.0", "plop": "^3.1.2", "postcss": "8.4.38", "prettier": "^2.8.8", "tailwindcss": "3.3.2", "typescript": "5.4.5"}, "lint-staged": {"**/*.{js,jsx,ts,tsx,html,css,json}": ["prettier --write"]}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}